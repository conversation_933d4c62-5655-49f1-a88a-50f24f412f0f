# -*- coding: utf-8 -*-
"""
消耗品使用模型
"""

from datetime import datetime
from typing import Optional
from .base_model import BaseModel

class ConsumableUsage(BaseModel):
    """消耗品使用模型类"""
    
    def __init__(self, **kwargs):
        # 基本属性
        self.id: Optional[int] = kwargs.get('id')
        self.material_id: int = kwargs.get('material_id')
        self.material_name: Optional[str] = kwargs.get('material_name')
        self.department_id: int = kwargs.get('department_id')
        self.department_name: Optional[str] = kwargs.get('department_name')
        self.user_id: int = kwargs.get('user_id')
        self.user_name: Optional[str] = kwargs.get('user_name')
        self.quantity: int = kwargs.get('quantity', 1)
        self.usage_date: datetime = kwargs.get('usage_date', datetime.now())
        self.purpose: Optional[str] = kwargs.get('purpose')
        self.project: Optional[str] = kwargs.get('project')
        self.cost_center: Optional[str] = kwargs.get('cost_center')
        self.remark: Optional[str] = kwargs.get('remark')
        self.created_at: Optional[datetime] = kwargs.get('created_at')
        self.updated_at: Optional[datetime] = kwargs.get('updated_at')
        
        super().__init__(**kwargs)
    
    def validate(self) -> bool:
        """验证使用数据"""
        if not self.material_id or not self.department_id or not self.user_id:
            return False
        if self.quantity <= 0:
            return False
        if not self.usage_date:
            return False
        return True
    
    def get_usage_info(self) -> str:
        """获取使用信息摘要"""
        info = f"{self.material_name} x {self.quantity}"
        if self.purpose:
            info += f" ({self.purpose})"
        return info
    
    def get_cost_info(self) -> dict:
        """获取成本信息"""
        return {
            'department': self.department_name,
            'project': self.project,
            'cost_center': self.cost_center,
            'user': self.user_name,
            'date': self.usage_date
        }
