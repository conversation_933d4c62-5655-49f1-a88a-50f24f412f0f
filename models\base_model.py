# -*- coding: utf-8 -*-
"""
基础模型类
"""

from datetime import datetime
from typing import Dict, Any, Optional

class BaseModel:
    """基础模型类，提供通用的数据模型功能"""
    
    def __init__(self, **kwargs):
        """初始化模型"""
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        for key, value in self.__dict__.items():
            if isinstance(value, datetime):
                result[key] = value.isoformat()
            else:
                result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """从字典创建模型实例"""
        return cls(**data)
    
    def update(self, **kwargs):
        """更新模型属性"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def validate(self) -> bool:
        """验证模型数据"""
        return True
    
    def __repr__(self):
        """字符串表示"""
        class_name = self.__class__.__name__
        attrs = ', '.join(f'{k}={v}' for k, v in self.__dict__.items())
        return f'{class_name}({attrs})'
