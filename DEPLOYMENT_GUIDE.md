# 金融企业单位物资管理系统 - 部署指南

## 系统概述

这是一个完整的金融企业单位物资管理系统，使用Flask框架开发，支持物资采购、登记、处置管理，物资分配到各科室，以及查询统计和报表生成功能。

## 系统要求

- Python 3.7+
- MySQL 5.7+ 或 MariaDB 10.2+
- 现代Web浏览器（Chrome、Firefox、Safari、Edge）

## 快速部署

### 1. 环境准备

确保已安装Python和MySQL服务器。

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 数据库初始化

运行数据库初始化脚本：

```bash
python init_database.py
```

### 4. 启动系统

```bash
python run.py
```

### 5. 访问系统

在浏览器中访问：http://127.0.0.1:5000

## 默认账户

- **管理员账户**
  - 用户名：admin
  - 密码：admin123
  - 权限：完整系统管理权限

- **员工账户**
  - 用户名：user1
  - 密码：user123
  - 权限：查看本科室物资信息

## 系统功能

### 1. 用户管理
- 用户登录/登出
- 角色权限控制（管理员/员工）
- 用户信息管理

### 2. 物资管理
- 物资采购登记
- 固定资产管理（自动生成资产编号）
- 消耗品管理
- 物资分配申请和审批
- 物资报废申请和审批

### 3. 科室管理
- 科室信息维护
- 科室物资分配

### 4. 供应商管理
- 供应商信息维护
- 供应商评价管理

### 5. 统计查询
- 按物资类型、名称、价格查询
- 按科室查询物资分配情况
- 物资使用统计
- 月度统计报表

### 6. 报表导出
- 物资清单报表
- 固定资产状态报表
- 消耗品使用报表
- Excel格式导出

## 配置说明

### 数据库配置

在 `config.py` 中修改数据库连接信息：

```python
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'your_password',  # 修改为您的MySQL密码
    'database': 'goods',
    'charset': 'utf8mb4',
    'autocommit': True
}
```

### 安全配置

在生产环境中，请修改以下配置：

1. 更改SECRET_KEY
2. 关闭DEBUG模式
3. 使用HTTPS
4. 配置防火墙

## 故障排除

### 1. 数据库连接失败

- 检查MySQL服务是否启动
- 验证数据库用户名和密码
- 确认数据库权限设置

### 2. 依赖包安装失败

- 升级pip：`pip install --upgrade pip`
- 使用国内镜像：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`

### 3. 端口占用

- 修改 `run.py` 中的端口号
- 或者终止占用端口的进程

## 系统架构

```
金融信息系统/
├── app.py              # 主应用文件
├── run.py              # 启动脚本
├── config.py           # 配置文件
├── database.py         # 数据库服务层
├── auth.py             # 用户认证模块
├── materials.py        # 物资管理模块
├── stats.py            # 统计查询模块
├── reports.py          # 报表生成模块
├── database_schema.sql # 数据库架构
├── init_database.py    # 数据库初始化脚本
├── test_system.py      # 系统测试脚本
├── requirements.txt    # 依赖包列表
├── templates/          # HTML模板
│   ├── base.html
│   ├── login.html
│   └── dashboard.html
└── README.md           # 项目说明
```

## 技术栈

- **后端框架**：Flask
- **数据库**：MySQL
- **前端框架**：Bootstrap 5
- **图标库**：Font Awesome
- **数据处理**：Pandas
- **Excel导出**：OpenPyXL
- **密码加密**：bcrypt

## 开发说明

### 添加新功能

1. 在相应的蓝图模块中添加路由
2. 创建对应的HTML模板
3. 更新数据库架构（如需要）
4. 编写测试用例

### 数据库迁移

如需修改数据库结构：

1. 备份现有数据
2. 修改 `database_schema.sql`
3. 重新运行 `init_database.py`

## 支持与维护

如遇到问题，请检查：

1. 系统日志输出
2. 数据库连接状态
3. 依赖包版本兼容性

## 许可证

本项目仅供学习和研究使用。
