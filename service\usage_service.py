# -*- coding: utf-8 -*-
"""
消耗品使用服务
"""

from typing import Optional, List, Dict, Any
from datetime import datetime, date
from .base_service import BaseService
from dao.usage_dao import UsageDAO
from dao.material_dao import MaterialDAO
from dao.department_dao import DepartmentDAO
from dao.user_dao import UserDAO
from models.usage_model import ConsumableUsage

class UsageService(BaseService):
    """消耗品使用业务逻辑服务"""
    
    def __init__(self):
        super().__init__()
        self.usage_dao = UsageDAO()
        self.material_dao = MaterialDAO()
        self.department_dao = DepartmentDAO()
        self.user_dao = UserDAO()
    
    def get_all_usages(self) -> List[ConsumableUsage]:
        """获取所有使用记录"""
        try:
            return self.usage_dao.find_all_with_details()
        except Exception as e:
            self.logger.error(f"获取使用记录失败: {e}")
            return []
    
    def get_usage_by_id(self, usage_id: int) -> Optional[ConsumableUsage]:
        """根据ID获取使用记录"""
        try:
            return self.usage_dao.find_by_id(usage_id)
        except Exception as e:
            self.logger.error(f"获取使用记录失败: {e}")
            return None
    
    def record_usage(self, usage_data: Dict[str, Any], operator_id: int) -> Dict[str, Any]:
        """记录消耗品使用"""
        try:
            # 验证必填字段
            required_fields = ['material_id', 'department_id', 'user_id', 'quantity', 'purpose']
            error = self.validate_required_fields(usage_data, required_fields)
            if error:
                return self.create_error_response(error)
            
            # 验证数量
            error = self.validate_positive_number(usage_data['quantity'], '使用数量')
            if error:
                return self.create_error_response(error)
            
            # 验证物资是否存在且为消耗品
            material = self.material_dao.find_by_id(usage_data['material_id'])
            if not material:
                return self.create_error_response("指定的物资不存在")
            
            if material.category != 'consumable':
                return self.create_error_response("只能记录消耗品的使用情况")
            
            if material.status != 'in_stock':
                return self.create_error_response("物资状态不可用")
            
            # 验证库存是否充足
            if material.quantity < usage_data['quantity']:
                return self.create_error_response(f"库存不足，当前库存: {material.quantity}")
            
            # 验证部门是否存在
            department = self.department_dao.find_by_id(usage_data['department_id'])
            if not department:
                return self.create_error_response("指定的部门不存在")
            
            # 验证用户是否存在
            user = self.user_dao.find_by_id(usage_data['user_id'])
            if not user:
                return self.create_error_response("指定的用户不存在")
            
            # 验证用途
            error = self.validate_string_length(usage_data['purpose'], '使用用途', 2, 200)
            if error:
                return self.create_error_response(error)
            
            # 设置使用日期（如果未提供）
            if 'usage_date' not in usage_data:
                usage_data['usage_date'] = datetime.now()
            
            # 创建使用记录
            usage = ConsumableUsage(**usage_data)
            
            # 验证使用数据
            if not usage.validate():
                return self.create_error_response("使用记录数据验证失败")
            
            # 保存使用记录
            created_usage = self.usage_dao.insert(usage)
            
            # 减少库存
            self.material_dao.update_quantity(usage_data['material_id'], -usage_data['quantity'])
            
            self.log_operation("记录消耗品使用", operator_id, 
                             f"物资: {material.name}, 数量: {usage_data['quantity']}")
            
            return self.create_success_response("使用记录创建成功", created_usage.to_dict())
            
        except Exception as e:
            return self.handle_service_error(e, "记录消耗品使用")
    
    def update_usage(self, usage_id: int, usage_data: Dict[str, Any], operator_id: int) -> Dict[str, Any]:
        """更新使用记录"""
        try:
            # 获取现有使用记录
            usage = self.usage_dao.find_by_id(usage_id)
            if not usage:
                return self.create_error_response("使用记录不存在")
            
            # 验证必填字段
            required_fields = ['material_id', 'department_id', 'user_id', 'quantity', 'purpose']
            error = self.validate_required_fields(usage_data, required_fields)
            if error:
                return self.create_error_response(error)
            
            # 验证数量
            error = self.validate_positive_number(usage_data['quantity'], '使用数量')
            if error:
                return self.create_error_response(error)
            
            # 验证物资是否存在
            material = self.material_dao.find_by_id(usage_data['material_id'])
            if not material:
                return self.create_error_response("指定的物资不存在")
            
            # 验证部门是否存在
            department = self.department_dao.find_by_id(usage_data['department_id'])
            if not department:
                return self.create_error_response("指定的部门不存在")
            
            # 验证用户是否存在
            user = self.user_dao.find_by_id(usage_data['user_id'])
            if not user:
                return self.create_error_response("指定的用户不存在")
            
            # 验证用途
            error = self.validate_string_length(usage_data['purpose'], '使用用途', 2, 200)
            if error:
                return self.create_error_response(error)
            
            # 如果数量发生变化，需要调整库存
            quantity_diff = usage_data['quantity'] - usage.quantity
            if quantity_diff != 0:
                # 检查库存是否充足（如果增加使用量）
                if quantity_diff > 0 and material.quantity < quantity_diff:
                    return self.create_error_response(f"库存不足，当前库存: {material.quantity}")
                
                # 调整库存
                self.material_dao.update_quantity(usage_data['material_id'], -quantity_diff)
            
            # 更新使用记录属性
            usage.update(**usage_data)
            
            # 验证使用数据
            if not usage.validate():
                return self.create_error_response("使用记录数据验证失败")
            
            # 保存使用记录
            updated_usage = self.usage_dao.update(usage)
            
            self.log_operation("更新使用记录", operator_id, f"记录ID: {usage_id}")
            
            return self.create_success_response("使用记录更新成功", updated_usage.to_dict())
            
        except Exception as e:
            return self.handle_service_error(e, "更新使用记录")
    
    def delete_usage(self, usage_id: int, operator_id: int) -> Dict[str, Any]:
        """删除使用记录"""
        try:
            # 获取使用记录
            usage = self.usage_dao.find_by_id(usage_id)
            if not usage:
                return self.create_error_response("使用记录不存在")
            
            # 恢复库存
            self.material_dao.update_quantity(usage.material_id, usage.quantity)
            
            # 删除记录
            success = self.usage_dao.delete(usage_id)
            
            if success:
                self.log_operation("删除使用记录", operator_id, f"记录ID: {usage_id}")
                return self.create_success_response("使用记录删除成功")
            else:
                return self.create_error_response("删除失败")
                
        except Exception as e:
            return self.handle_service_error(e, "删除使用记录")
    
    def get_usages_by_department(self, department_id: int) -> List[ConsumableUsage]:
        """根据部门获取使用记录"""
        try:
            return self.usage_dao.find_by_department(department_id)
        except Exception as e:
            self.logger.error(f"获取部门使用记录失败: {e}")
            return []
    
    def get_usages_by_user(self, user_id: int) -> List[ConsumableUsage]:
        """根据用户获取使用记录"""
        try:
            return self.usage_dao.find_by_user(user_id)
        except Exception as e:
            self.logger.error(f"获取用户使用记录失败: {e}")
            return []
    
    def get_usages_by_date_range(self, start_date: date, end_date: date) -> List[ConsumableUsage]:
        """根据日期范围获取使用记录"""
        try:
            return self.usage_dao.find_by_date_range(start_date, end_date)
        except Exception as e:
            self.logger.error(f"获取使用记录失败: {e}")
            return []
    
    def get_usage_statistics_by_department(self, start_date: date = None, end_date: date = None) -> List[Dict[str, Any]]:
        """按部门统计使用情况"""
        try:
            return self.usage_dao.get_usage_statistics_by_department(start_date, end_date)
        except Exception as e:
            self.logger.error(f"获取部门使用统计失败: {e}")
            return []
    
    def get_usage_statistics_by_material(self, start_date: date = None, end_date: date = None) -> List[Dict[str, Any]]:
        """按物资统计使用情况"""
        try:
            return self.usage_dao.get_usage_statistics_by_material(start_date, end_date)
        except Exception as e:
            self.logger.error(f"获取物资使用统计失败: {e}")
            return []
