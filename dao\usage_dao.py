# -*- coding: utf-8 -*-
"""
消耗品使用DAO
"""

from typing import List, Dict, Any
from datetime import datetime, date
from .base_dao import BaseDAO
from models.usage_model import ConsumableUsage

class UsageDAO(BaseDAO):
    """消耗品使用数据访问对象"""
    
    def __init__(self):
        super().__init__(ConsumableUsage, 'consumable_usage')
    
    def insert(self, usage: ConsumableUsage) -> ConsumableUsage:
        """插入新使用记录"""
        usage.created_at = datetime.now()
        usage.updated_at = datetime.now()
        
        data = {
            'material_id': usage.material_id,
            'department_id': usage.department_id,
            'user_id': usage.user_id,
            'quantity': usage.quantity,
            'usage_date': usage.usage_date,
            'purpose': usage.purpose,
            'project': usage.project,
            'cost_center': usage.cost_center,
            'remark': usage.remark,
            'created_at': usage.created_at,
            'updated_at': usage.updated_at
        }
        
        query, params = self.build_insert_query(data)
        usage.id = self.db.execute_insert(query, params)
        return usage
    
    def update(self, usage: ConsumableUsage) -> ConsumableUsage:
        """更新使用记录"""
        usage.updated_at = datetime.now()
        
        data = {
            'material_id': usage.material_id,
            'department_id': usage.department_id,
            'user_id': usage.user_id,
            'quantity': usage.quantity,
            'usage_date': usage.usage_date,
            'purpose': usage.purpose,
            'project': usage.project,
            'cost_center': usage.cost_center,
            'remark': usage.remark,
            'updated_at': usage.updated_at
        }
        
        query, params = self.build_update_query(data, usage.id)
        self.db.execute_update(query, params)
        return usage
    
    def find_all_with_details(self) -> List[ConsumableUsage]:
        """查找所有使用记录（包含详细信息）"""
        query = """
            SELECT 
                cu.*,
                m.name as material_name,
                d.name as department_name,
                u.real_name as user_name
            FROM consumable_usage cu
            LEFT JOIN materials m ON cu.material_id = m.id
            LEFT JOIN departments d ON cu.department_id = d.id
            LEFT JOIN users u ON cu.user_id = u.id
            ORDER BY cu.usage_date DESC
        """
        results = self.db.execute_query(query, fetch_all=True)
        return [ConsumableUsage(**row) for row in results] if results else []
    
    def find_by_department(self, department_id: int) -> List[ConsumableUsage]:
        """根据部门查找使用记录"""
        query = """
            SELECT 
                cu.*,
                m.name as material_name,
                d.name as department_name,
                u.real_name as user_name
            FROM consumable_usage cu
            LEFT JOIN materials m ON cu.material_id = m.id
            LEFT JOIN departments d ON cu.department_id = d.id
            LEFT JOIN users u ON cu.user_id = u.id
            WHERE cu.department_id = %s
            ORDER BY cu.usage_date DESC
        """
        results = self.db.execute_query(query, (department_id,), fetch_all=True)
        return [ConsumableUsage(**row) for row in results] if results else []
    
    def find_by_user(self, user_id: int) -> List[ConsumableUsage]:
        """根据用户查找使用记录"""
        query = """
            SELECT 
                cu.*,
                m.name as material_name,
                d.name as department_name,
                u.real_name as user_name
            FROM consumable_usage cu
            LEFT JOIN materials m ON cu.material_id = m.id
            LEFT JOIN departments d ON cu.department_id = d.id
            LEFT JOIN users u ON cu.user_id = u.id
            WHERE cu.user_id = %s
            ORDER BY cu.usage_date DESC
        """
        results = self.db.execute_query(query, (user_id,), fetch_all=True)
        return [ConsumableUsage(**row) for row in results] if results else []
    
    def find_by_date_range(self, start_date: date, end_date: date) -> List[ConsumableUsage]:
        """根据日期范围查找使用记录"""
        query = """
            SELECT 
                cu.*,
                m.name as material_name,
                d.name as department_name,
                u.real_name as user_name
            FROM consumable_usage cu
            LEFT JOIN materials m ON cu.material_id = m.id
            LEFT JOIN departments d ON cu.department_id = d.id
            LEFT JOIN users u ON cu.user_id = u.id
            WHERE DATE(cu.usage_date) BETWEEN %s AND %s
            ORDER BY cu.usage_date DESC
        """
        results = self.db.execute_query(query, (start_date, end_date), fetch_all=True)
        return [ConsumableUsage(**row) for row in results] if results else []
    
    def get_usage_statistics_by_department(self, start_date: date = None, end_date: date = None) -> List[Dict[str, Any]]:
        """按部门统计使用情况"""
        query = """
            SELECT 
                d.name as department_name,
                COUNT(*) as usage_count,
                SUM(cu.quantity) as total_quantity,
                COUNT(DISTINCT cu.material_id) as material_types
            FROM consumable_usage cu
            LEFT JOIN departments d ON cu.department_id = d.id
        """
        
        params = []
        if start_date and end_date:
            query += " WHERE DATE(cu.usage_date) BETWEEN %s AND %s"
            params = [start_date, end_date]
        
        query += " GROUP BY cu.department_id, d.name ORDER BY total_quantity DESC"
        
        return self.db.execute_query(query, tuple(params) if params else None, fetch_all=True)
    
    def get_usage_statistics_by_material(self, start_date: date = None, end_date: date = None) -> List[Dict[str, Any]]:
        """按物资统计使用情况"""
        query = """
            SELECT 
                m.name as material_name,
                m.unit,
                COUNT(*) as usage_count,
                SUM(cu.quantity) as total_quantity,
                COUNT(DISTINCT cu.department_id) as department_count
            FROM consumable_usage cu
            LEFT JOIN materials m ON cu.material_id = m.id
        """
        
        params = []
        if start_date and end_date:
            query += " WHERE DATE(cu.usage_date) BETWEEN %s AND %s"
            params = [start_date, end_date]
        
        query += " GROUP BY cu.material_id, m.name, m.unit ORDER BY total_quantity DESC"
        
        return self.db.execute_query(query, tuple(params) if params else None, fetch_all=True)
