# -*- coding: utf-8 -*-
"""
物资分配控制器
"""

from flask import Blueprint, request, redirect, url_for, flash, session
from .base_action import BaseAction
from service.allocation_service import AllocationService
from service.material_service import MaterialService
from service.department_service import DepartmentService

class AllocationAction(BaseAction):
    """物资分配控制器"""
    
    def __init__(self):
        super().__init__()
        self.allocation_service = AllocationService()
        self.material_service = MaterialService()
        self.department_service = DepartmentService()
        self.blueprint = Blueprint('allocation', __name__, url_prefix='/allocation')
        self._register_routes()
    
    def _register_routes(self):
        """注册路由"""
        self.blueprint.add_url_rule('/', 'list', self.list_allocations, methods=['GET'])
        self.blueprint.add_url_rule('/create', 'create', self.create_allocation, methods=['GET', 'POST'])
        self.blueprint.add_url_rule('/<int:allocation_id>', 'detail', self.allocation_detail, methods=['GET'])
        self.blueprint.add_url_rule('/<int:allocation_id>/approve', 'approve', self.approve_allocation, methods=['POST'])
        self.blueprint.add_url_rule('/<int:allocation_id>/reject', 'reject', self.reject_allocation, methods=['POST'])
        self.blueprint.add_url_rule('/<int:allocation_id>/cancel', 'cancel', self.cancel_allocation, methods=['POST'])
        self.blueprint.add_url_rule('/pending', 'pending', self.pending_allocations, methods=['GET'])
        self.blueprint.add_url_rule('/my', 'my_allocations', self.my_allocations, methods=['GET'])
    
    def list_allocations(self):
        """分配列表"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            query_params = self.get_query_params()
            page = int(query_params.get('page', 1))
            per_page = int(query_params.get('per_page', 20))
            status = query_params.get('status')
            
            # 获取分配列表
            if self.is_admin():
                allocations = self.allocation_service.get_all_allocations()
            else:
                # 普通用户只能看到自己部门的分配
                department_id = session.get('department_id')
                if department_id:
                    allocations = self.allocation_service.get_allocations_by_department(department_id)
                else:
                    allocations = []
            
            # 过滤
            if status:
                allocations = [a for a in allocations if a.status == status]
            
            # 分页
            paginated_data = self.paginate_data([a.to_dict() for a in allocations], page, per_page)
            
            if request.is_json:
                return paginated_data
            else:
                return self.render_page('allocation/list.html', 
                                      allocations=paginated_data['data'],
                                      pagination=paginated_data['pagination'],
                                      current_status=status)
                                      
        except Exception as e:
            return self.handle_exception(e, "获取分配列表")
    
    def create_allocation(self):
        """创建分配申请"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        if request.method == 'GET':
            materials = self.material_service.get_available_materials()
            departments = self.department_service.get_active_departments()
            return self.render_page('allocation/create.html', materials=materials, departments=departments)
        
        try:
            data = self.get_request_data()
            
            # 调用分配服务创建申请
            result = self.allocation_service.create_allocation_request(data, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "分配申请创建成功",
                url_for('allocation.list') if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "创建分配申请")
    
    def allocation_detail(self, allocation_id):
        """分配详情"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            allocation = self.allocation_service.get_allocation_by_id(allocation_id)
            
            if not allocation:
                if request.is_json:
                    return self.error_response("分配记录不存在", 404)
                else:
                    flash("分配记录不存在", 'error')
                    return redirect(url_for('allocation.list'))
            
            # 权限检查：普通用户只能查看自己部门的分配
            if not self.is_admin():
                user_dept_id = session.get('department_id')
                if allocation.department_id != user_dept_id:
                    if request.is_json:
                        return self.error_response("权限不足", 403)
                    else:
                        flash("权限不足", 'error')
                        return redirect(url_for('allocation.list'))
            
            if request.is_json:
                return self.success_response("获取成功", allocation.to_dict())
            else:
                return self.render_page('allocation/detail.html', allocation=allocation)
                
        except Exception as e:
            return self.handle_exception(e, "获取分配详情")
    
    def approve_allocation(self, allocation_id):
        """审批分配申请"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('allocation.list'))
        
        try:
            data = self.get_request_data()
            comment = data.get('comment', '')
            
            # 调用分配服务审批
            result = self.allocation_service.approve_allocation(allocation_id, self.get_current_user_id(), comment)
            
            return self.handle_service_response(
                result,
                "分配申请审批通过",
                url_for('allocation.detail', allocation_id=allocation_id) if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "审批分配申请")
    
    def reject_allocation(self, allocation_id):
        """拒绝分配申请"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('allocation.list'))
        
        try:
            data = self.get_request_data()
            comment = data.get('comment', '')
            
            # 调用分配服务拒绝
            result = self.allocation_service.reject_allocation(allocation_id, self.get_current_user_id(), comment)
            
            return self.handle_service_response(
                result,
                "分配申请已拒绝",
                url_for('allocation.detail', allocation_id=allocation_id) if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "拒绝分配申请")
    
    def cancel_allocation(self, allocation_id):
        """取消分配申请"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            # 调用分配服务取消
            result = self.allocation_service.cancel_allocation(allocation_id, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "分配申请已取消",
                url_for('allocation.list') if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "取消分配申请")
    
    def pending_allocations(self):
        """待审批的分配申请"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('allocation.list'))
        
        try:
            allocations = self.allocation_service.get_pending_allocations()
            
            if request.is_json:
                return self.success_response("获取成功", [a.to_dict() for a in allocations])
            else:
                return self.render_page('allocation/pending.html', allocations=allocations)
                
        except Exception as e:
            return self.handle_exception(e, "获取待审批分配")
    
    def my_allocations(self):
        """我的分配申请"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            allocations = self.allocation_service.get_allocations_by_applicant(self.get_current_user_id())
            
            if request.is_json:
                return self.success_response("获取成功", [a.to_dict() for a in allocations])
            else:
                return self.render_page('allocation/my_allocations.html', allocations=allocations)
                
        except Exception as e:
            return self.handle_exception(e, "获取我的分配申请")
