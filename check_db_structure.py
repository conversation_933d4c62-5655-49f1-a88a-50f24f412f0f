#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构
"""

from database import Database

def check_table_structure():
    """检查数据库表结构"""
    db = Database()
    conn = db.get_connection()
    cursor = conn.cursor()
    
    try:
        # 检查departments表结构
        print("=== Departments Table Structure ===")
        cursor.execute("DESCRIBE departments")
        for row in cursor.fetchall():
            print(row)
        
        print("\n=== Users Table Structure ===")
        cursor.execute("DESCRIBE users")
        for row in cursor.fetchall():
            print(row)
            
        print("\n=== Materials Table Structure ===")
        cursor.execute("DESCRIBE materials")
        for row in cursor.fetchall():
            print(row)

        # 检查material_allocations表结构
        print("\n=== Material_Allocations Table Structure ===")
        cursor.execute("DESCRIBE material_allocations")
        for row in cursor.fetchall():
            print(row)

        # 检查material_scraps表结构
        print("\n=== Material_Scraps Table Structure ===")
        cursor.execute("DESCRIBE material_scraps")
        for row in cursor.fetchall():
            print(row)

        # 检查consumable_usage表结构
        print("\n=== Consumable_Usage Table Structure ===")
        cursor.execute("DESCRIBE consumable_usage")
        for row in cursor.fetchall():
            print(row)

        print("\n=== All Tables ===")
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        for table in tables:
            print(table)
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        cursor.close()
        conn.close()

if __name__ == '__main__':
    check_table_structure()
