# -*- coding: utf-8 -*-
"""
物资模型
"""

from datetime import datetime
from typing import Optional
from decimal import Decimal
from .base_model import BaseModel

class Material(BaseModel):
    """物资模型类"""
    
    def __init__(self, **kwargs):
        # 基本属性
        self.id: Optional[int] = kwargs.get('id')
        self.name: str = kwargs.get('name', '')
        self.category: str = kwargs.get('category', 'consumable')  # fixed_asset, consumable
        self.asset_number: Optional[str] = kwargs.get('asset_number')
        self.specification: Optional[str] = kwargs.get('specification')
        self.unit: str = kwargs.get('unit', '个')
        self.price: Decimal = Decimal(str(kwargs.get('price', 0)))
        self.quantity: int = kwargs.get('quantity', 0)
        self.supplier_id: Optional[int] = kwargs.get('supplier_id')
        self.supplier_name: Optional[str] = kwargs.get('supplier_name')
        self.purchase_date: Optional[datetime] = kwargs.get('purchase_date')
        self.warranty_period: Optional[int] = kwargs.get('warranty_period')  # 保修期（月）
        self.location: Optional[str] = kwargs.get('location')
        self.status: str = kwargs.get('status', 'in_stock')  # in_stock, allocated, scrapped
        self.remark: Optional[str] = kwargs.get('remark')
        self.created_at: Optional[datetime] = kwargs.get('created_at')
        self.updated_at: Optional[datetime] = kwargs.get('updated_at')
        
        super().__init__(**kwargs)
    
    def is_fixed_asset(self) -> bool:
        """判断是否为固定资产"""
        return self.category == 'fixed_asset'
    
    def is_consumable(self) -> bool:
        """判断是否为消耗品"""
        return self.category == 'consumable'
    
    def is_available(self) -> bool:
        """判断是否可用"""
        return self.status == 'in_stock' and self.quantity > 0
    
    def get_total_value(self) -> Decimal:
        """获取总价值"""
        return self.price * self.quantity
    
    def validate(self) -> bool:
        """验证物资数据"""
        if not self.name or len(self.name) < 2:
            return False
        if self.category not in ['fixed_asset', 'consumable']:
            return False
        if self.price < 0:
            return False
        if self.quantity < 0:
            return False
        if self.status not in ['in_stock', 'allocated', 'scrapped']:
            return False
        return True
    
    def get_category_display(self) -> str:
        """获取类别显示名称"""
        category_map = {
            'fixed_asset': '固定资产',
            'consumable': '消耗品'
        }
        return category_map.get(self.category, self.category)
    
    def get_status_display(self) -> str:
        """获取状态显示名称"""
        status_map = {
            'in_stock': '库存中',
            'allocated': '已分配',
            'scrapped': '已报废'
        }
        return status_map.get(self.status, self.status)
    
    def generate_asset_number(self) -> str:
        """生成资产编号（仅用于固定资产）"""
        if self.is_fixed_asset() and not self.asset_number:
            # 格式：FA + 6位数字
            import random
            return f"FA{random.randint(100000, 999999):06d}"
        return self.asset_number
