#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
金融企业单位物资管理系统
主应用文件 - MVC架构版本
"""

from flask import Flask
from flask_cors import CORS
import json
from datetime import datetime, date
import logging

# 导入新的MVC控制器
from action import (
    MainAction, AuthAction, MaterialAction, UserAction,
    DepartmentAction, SupplierAction, AllocationAction,
    ScrapAction, UsageAction, ReportAction
)

# 创建Flask应用
app = Flask(__name__)
app.secret_key = 'your-secret-key-here-change-in-production'  # 在生产环境中应该使用更安全的密钥
CORS(app)

# 配置日志
logging.basicConfig(level=logging.INFO)

# 初始化控制器并注册蓝图
def register_blueprints():
    """注册所有蓝图"""
    # 创建控制器实例
    main_action = MainAction()
    auth_action = AuthAction()
    material_action = MaterialAction()
    user_action = UserAction()
    department_action = DepartmentAction()
    supplier_action = SupplierAction()
    allocation_action = AllocationAction()
    scrap_action = ScrapAction()
    usage_action = UsageAction()
    report_action = ReportAction()

    # 注册蓝图
    app.register_blueprint(main_action.blueprint)
    app.register_blueprint(auth_action.blueprint)
    app.register_blueprint(material_action.blueprint)
    app.register_blueprint(user_action.blueprint)
    app.register_blueprint(department_action.blueprint)
    app.register_blueprint(supplier_action.blueprint)
    app.register_blueprint(allocation_action.blueprint)
    app.register_blueprint(scrap_action.blueprint)
    app.register_blueprint(usage_action.blueprint)
    app.register_blueprint(report_action.blueprint)

# 注册蓝图
register_blueprints()

# 自定义JSON序列化器
class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        return super().default(obj)

app.json_encoder = DateTimeEncoder

# 错误处理
@app.errorhandler(404)
def not_found_error(error):
    """404错误处理"""
    return {'error': 'Page not found'}, 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return {'error': 'Internal server error'}, 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
