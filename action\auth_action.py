# -*- coding: utf-8 -*-
"""
认证控制器
"""

from flask import Blueprint, request, session, redirect, url_for, flash
from .base_action import BaseAction
from service.user_service import UserService

class AuthAction(BaseAction):
    """认证控制器"""
    
    def __init__(self):
        super().__init__()
        self.user_service = UserService()
        self.blueprint = Blueprint('auth', __name__, url_prefix='/auth')
        self._register_routes()
    
    def _register_routes(self):
        """注册路由"""
        self.blueprint.add_url_rule('/login', 'login', self.login, methods=['GET', 'POST'])
        self.blueprint.add_url_rule('/logout', 'logout', self.logout, methods=['POST'])
        self.blueprint.add_url_rule('/register', 'register', self.register, methods=['GET', 'POST'])
        self.blueprint.add_url_rule('/change-password', 'change_password', self.change_password, methods=['POST'])
    
    def login(self):
        """用户登录"""
        if request.method == 'GET':
            # 如果已登录，重定向到主页
            if self.is_logged_in():
                return redirect(url_for('main.index'))
            return self.render_page('auth/login.html')
        
        try:
            data = self.get_request_data()
            
            # 验证必需参数
            error = self.validate_required_params(data, ['username', 'password'])
            if error:
                if request.is_json:
                    return self.error_response(error)
                else:
                    flash(error, 'error')
                    return self.render_page('auth/login.html')
            
            # 调用用户服务进行认证
            user = self.user_service.authenticate(data['username'], data['password'])

            if user:
                # 设置会话
                session['user_id'] = user.id
                session['username'] = user.username
                session['user_role'] = user.role
                session['department_id'] = user.department_id

                self.log_action("用户登录", f"用户名: {data['username']}")

                if request.is_json:
                    user_data = {
                        'id': user.id,
                        'username': user.username,
                        'role': user.role,
                        'department_id': user.department_id
                    }
                    return self.success_response("登录成功", user_data)
                else:
                    flash("登录成功", 'success')
                    # 重定向到原来要访问的页面或主页
                    next_page = request.args.get('next')
                    return redirect(next_page or url_for('main.index'))
            else:
                error_message = "用户名或密码错误"
                if request.is_json:
                    return self.error_response(error_message, 401)
                else:
                    flash(error_message, 'error')
                    return self.render_page('auth/login.html')
                    
        except Exception as e:
            return self.handle_exception(e, "用户登录")
    
    def logout(self):
        """用户登出"""
        try:
            username = session.get('username', '未知用户')
            
            # 清除会话
            session.clear()
            
            self.log_action("用户登出", f"用户名: {username}")
            
            if request.is_json:
                return self.success_response("登出成功")
            else:
                flash("已成功登出", 'success')
                return redirect(url_for('auth.login'))
                
        except Exception as e:
            return self.handle_exception(e, "用户登出")
    
    def register(self):
        """用户注册（仅管理员可用）"""
        if request.method == 'GET':
            # 检查权限
            if not self.is_admin():
                flash('权限不足', 'error')
                return redirect(url_for('main.index'))
            
            return self.render_page('auth/register.html')
        
        try:
            # 检查权限
            if not self.is_admin():
                if request.is_json:
                    return self.error_response('权限不足', 403)
                else:
                    flash('权限不足', 'error')
                    return redirect(url_for('main.index'))
            
            data = self.get_request_data()
            
            # 验证必需参数
            required_fields = ['username', 'password', 'email', 'full_name', 'role']
            error = self.validate_required_params(data, required_fields)
            if error:
                if request.is_json:
                    return self.error_response(error)
                else:
                    flash(error, 'error')
                    return self.render_page('auth/register.html')
            
            # 调用用户服务创建用户
            result = self.user_service.create_user(data, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "用户注册成功",
                url_for('user.list') if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "用户注册")
    
    def change_password(self):
        """修改密码"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))

        try:
            data = self.get_request_data()
            
            # 验证必需参数
            required_fields = ['current_password', 'new_password']
            error = self.validate_required_params(data, required_fields)
            if error:
                return self.error_response(error)
            
            # 调用用户服务修改密码
            result = self.user_service.change_password(
                self.get_current_user_id(),
                data['current_password'],
                data['new_password']
            )
            
            if result['success']:
                self.log_action("修改密码")
                
                if request.is_json:
                    return self.success_response("密码修改成功")
                else:
                    flash("密码修改成功", 'success')
                    return redirect(url_for('user.profile'))
            else:
                return self.handle_service_response(result)
                
        except Exception as e:
            return self.handle_exception(e, "修改密码")
