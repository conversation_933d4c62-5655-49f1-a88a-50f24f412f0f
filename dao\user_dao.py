# -*- coding: utf-8 -*-
"""
用户DAO
"""

from typing import Optional, List
import bcrypt
from datetime import datetime
from .base_dao import BaseDAO
from models.user_model import User

class UserDAO(BaseDAO):
    """用户数据访问对象"""
    
    def __init__(self):
        super().__init__(User, 'users')
    
    def insert(self, user: User) -> User:
        """插入新用户"""
        # 加密密码
        if user.password:
            hashed_password = bcrypt.hashpw(user.password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            user.password = hashed_password
        
        user.created_at = datetime.now()
        user.updated_at = datetime.now()
        
        data = {
            'username': user.username,
            'password': user.password,
            'real_name': user.real_name,
            'role': user.role,
            'department_id': user.department_id,
            'phone': user.phone,
            'email': user.email,
            'status': user.status,
            'created_at': user.created_at,
            'updated_at': user.updated_at
        }
        
        query, params = self.build_insert_query(data)
        user.id = self.db.execute_insert(query, params)
        return user
    
    def update(self, user: User) -> User:
        """更新用户"""
        user.updated_at = datetime.now()
        
        data = {
            'username': user.username,
            'real_name': user.real_name,
            'role': user.role,
            'department_id': user.department_id,
            'phone': user.phone,
            'email': user.email,
            'status': user.status,
            'updated_at': user.updated_at
        }
        
        # 如果密码有变化，则更新密码
        if user.password and not user.password.startswith('$2b$'):
            hashed_password = bcrypt.hashpw(user.password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            data['password'] = hashed_password
        
        query, params = self.build_update_query(data, user.id)
        self.db.execute_update(query, params)
        return user
    
    def find_by_username(self, username: str) -> Optional[User]:
        """根据用户名查找用户"""
        query = """
            SELECT u.*, d.name as department_name 
            FROM users u 
            LEFT JOIN departments d ON u.department_id = d.id 
            WHERE u.username = %s
        """
        result = self.db.execute_query(query, (username,), fetch_one=True)
        return User(**result) if result else None
    
    def authenticate(self, username: str, password: str) -> Optional[User]:
        """用户认证"""
        user = self.find_by_username(username)
        if user and user.is_active():
            if bcrypt.checkpw(password.encode('utf-8'), user.password.encode('utf-8')):
                return user
        return None
    
    def find_by_department(self, department_id: int) -> List[User]:
        """根据部门查找用户"""
        query = """
            SELECT u.*, d.name as department_name 
            FROM users u 
            LEFT JOIN departments d ON u.department_id = d.id 
            WHERE u.department_id = %s AND u.status = 'active'
        """
        results = self.db.execute_query(query, (department_id,), fetch_all=True)
        return [User(**row) for row in results] if results else []
    
    def find_by_role(self, role: str) -> List[User]:
        """根据角色查找用户"""
        query = """
            SELECT u.*, d.name as department_name 
            FROM users u 
            LEFT JOIN departments d ON u.department_id = d.id 
            WHERE u.role = %s AND u.status = 'active'
        """
        results = self.db.execute_query(query, (role,), fetch_all=True)
        return [User(**row) for row in results] if results else []
    
    def find_all_with_department(self) -> List[User]:
        """查找所有用户（包含部门信息）"""
        query = """
            SELECT u.*, d.name as department_name 
            FROM users u 
            LEFT JOIN departments d ON u.department_id = d.id 
            ORDER BY u.created_at DESC
        """
        results = self.db.execute_query(query, fetch_all=True)
        return [User(**row) for row in results] if results else []
    
    def update_password(self, user_id: int, new_password: str) -> bool:
        """更新用户密码"""
        hashed_password = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        query = "UPDATE users SET password = %s, updated_at = %s WHERE id = %s"
        result = self.db.execute_update(query, (hashed_password, datetime.now(), user_id))
        return result > 0
    
    def update_status(self, user_id: int, status: str) -> bool:
        """更新用户状态"""
        query = "UPDATE users SET status = %s, updated_at = %s WHERE id = %s"
        result = self.db.execute_update(query, (status, datetime.now(), user_id))
        return result > 0
    
    def check_username_exists(self, username: str, exclude_id: Optional[int] = None) -> bool:
        """检查用户名是否存在"""
        query = "SELECT COUNT(*) as count FROM users WHERE username = %s"
        params = [username]
        
        if exclude_id:
            query += " AND id != %s"
            params.append(exclude_id)
        
        result = self.db.execute_query(query, tuple(params), fetch_one=True)
        return result['count'] > 0 if result else False
