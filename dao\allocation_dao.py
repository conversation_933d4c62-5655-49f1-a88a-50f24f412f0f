# -*- coding: utf-8 -*-
"""
物资分配DAO
"""

from typing import Optional, List
from datetime import datetime
from .base_dao import BaseDAO
from models.allocation_model import MaterialAllocation

class AllocationDAO(BaseDAO):
    """物资分配数据访问对象"""
    
    def __init__(self):
        super().__init__(MaterialAllocation, 'material_allocations')
    
    def insert(self, allocation: MaterialAllocation) -> MaterialAllocation:
        """插入新分配记录"""
        allocation.created_at = datetime.now()
        allocation.updated_at = datetime.now()
        
        data = {
            'material_id': allocation.material_id,
            'department_id': allocation.department_id,
            'quantity': allocation.quantity,
            'applicant_id': allocation.applicant_id,
            'application_date': allocation.application_date,
            'reason': allocation.reason,
            'status': allocation.status,
            'created_at': allocation.created_at,
            'updated_at': allocation.updated_at
        }
        
        query, params = self.build_insert_query(data)
        allocation.id = self.db.execute_insert(query, params)
        return allocation
    
    def update(self, allocation: MaterialAllocation) -> MaterialAllocation:
        """更新分配记录"""
        allocation.updated_at = datetime.now()
        
        data = {
            'material_id': allocation.material_id,
            'department_id': allocation.department_id,
            'quantity': allocation.quantity,
            'applicant_id': allocation.applicant_id,
            'application_date': allocation.application_date,
            'reason': allocation.reason,
            'status': allocation.status,
            'approver_id': allocation.approver_id,
            'approval_date': allocation.approval_date,
            'approval_comment': allocation.approval_comment,
            'recipient_id': allocation.recipient_id,
            'receive_date': allocation.receive_date,
            'updated_at': allocation.updated_at
        }
        
        query, params = self.build_update_query(data, allocation.id)
        self.db.execute_update(query, params)
        return allocation
    
    def find_all_with_details(self) -> List[MaterialAllocation]:
        """查找所有分配记录（包含详细信息）"""
        query = """
            SELECT 
                ma.*,
                m.name as material_name,
                d.name as department_name,
                u1.real_name as applicant_name,
                u2.real_name as approver_name,
                u3.real_name as receiver_name
            FROM material_allocations ma
            LEFT JOIN materials m ON ma.material_id = m.id
            LEFT JOIN departments d ON ma.department_id = d.id
            LEFT JOIN users u1 ON ma.allocated_by = u1.id
            LEFT JOIN users u2 ON ma.approver_id = u2.id
            LEFT JOIN users u3 ON ma.recipient_id = u3.id
            ORDER BY ma.application_date DESC
        """
        results = self.db.execute_query(query, fetch_all=True)
        return [MaterialAllocation(**row) for row in results] if results else []
    
    def find_by_status(self, status: str) -> List[MaterialAllocation]:
        """根据状态查找分配记录"""
        query = """
            SELECT 
                ma.*,
                m.name as material_name,
                d.name as department_name,
                u1.real_name as applicant_name,
                u2.real_name as approver_name,
                u3.real_name as receiver_name
            FROM material_allocations ma
            LEFT JOIN materials m ON ma.material_id = m.id
            LEFT JOIN departments d ON ma.department_id = d.id
            LEFT JOIN users u1 ON ma.allocated_by = u1.id
            LEFT JOIN users u2 ON ma.approver_id = u2.id
            LEFT JOIN users u3 ON ma.recipient_id = u3.id
            WHERE ma.status = %s
            ORDER BY ma.application_date DESC
        """
        results = self.db.execute_query(query, (status,), fetch_all=True)
        return [MaterialAllocation(**row) for row in results] if results else []
    
    def find_by_department(self, department_id: int) -> List[MaterialAllocation]:
        """根据部门查找分配记录"""
        query = """
            SELECT 
                ma.*,
                m.name as material_name,
                d.name as department_name,
                u1.real_name as applicant_name,
                u2.real_name as approver_name,
                u3.real_name as receiver_name
            FROM material_allocations ma
            LEFT JOIN materials m ON ma.material_id = m.id
            LEFT JOIN departments d ON ma.department_id = d.id
            LEFT JOIN users u1 ON ma.allocated_by = u1.id
            LEFT JOIN users u2 ON ma.approver_id = u2.id
            LEFT JOIN users u3 ON ma.recipient_id = u3.id
            WHERE ma.department_id = %s
            ORDER BY ma.application_date DESC
        """
        results = self.db.execute_query(query, (department_id,), fetch_all=True)
        return [MaterialAllocation(**row) for row in results] if results else []
    
    def find_by_applicant(self, applicant_id: int) -> List[MaterialAllocation]:
        """根据申请人查找分配记录"""
        query = """
            SELECT 
                ma.*,
                m.name as material_name,
                d.name as department_name,
                u1.real_name as applicant_name,
                u2.real_name as approver_name,
                u3.real_name as receiver_name
            FROM material_allocations ma
            LEFT JOIN materials m ON ma.material_id = m.id
            LEFT JOIN departments d ON ma.department_id = d.id
            LEFT JOIN users u1 ON ma.allocated_by = u1.id
            LEFT JOIN users u2 ON ma.approver_id = u2.id
            LEFT JOIN users u3 ON ma.recipient_id = u3.id
            WHERE ma.allocated_by = %s
            ORDER BY ma.application_date DESC
        """
        results = self.db.execute_query(query, (applicant_id,), fetch_all=True)
        return [MaterialAllocation(**row) for row in results] if results else []
    
    def approve_allocation(self, allocation_id: int, approver_id: int, comment: str = None) -> bool:
        """审批分配申请"""
        query = """
            UPDATE material_allocations 
            SET status = 'approved', approver_id = %s, approval_date = %s, 
                approval_comment = %s, updated_at = %s 
            WHERE id = %s AND status = 'pending'
        """
        result = self.db.execute_update(query, (
            approver_id, datetime.now(), comment, datetime.now(), allocation_id
        ))
        return result > 0
    
    def reject_allocation(self, allocation_id: int, approver_id: int, comment: str = None) -> bool:
        """拒绝分配申请"""
        query = """
            UPDATE material_allocations 
            SET status = 'rejected', approver_id = %s, approval_date = %s, 
                approval_comment = %s, updated_at = %s 
            WHERE id = %s AND status = 'pending'
        """
        result = self.db.execute_update(query, (
            approver_id, datetime.now(), comment, datetime.now(), allocation_id
        ))
        return result > 0
    
    def receive_allocation(self, allocation_id: int, recipient_id: int) -> bool:
        """接收分配的物资"""
        query = """
            UPDATE material_allocations
            SET status = 'received', recipient_id = %s, updated_at = %s
            WHERE id = %s AND status = 'approved'
        """
        result = self.db.execute_update(query, (
            recipient_id, datetime.now(), allocation_id
        ))
        return result > 0
