# -*- coding: utf-8 -*-
"""
物资使用控制器
"""

from flask import Blueprint, request, redirect, url_for, flash, session
from .base_action import BaseAction
from service.usage_service import UsageService
from service.material_service import MaterialService
from service.department_service import DepartmentService

class UsageAction(BaseAction):
    """物资使用控制器"""
    
    def __init__(self):
        super().__init__()
        self.usage_service = UsageService()
        self.material_service = MaterialService()
        self.department_service = DepartmentService()
        self.blueprint = Blueprint('usage', __name__, url_prefix='/usage')
        self._register_routes()
    
    def _register_routes(self):
        """注册路由"""
        self.blueprint.add_url_rule('/', 'list', self.list_usages, methods=['GET'])
        self.blueprint.add_url_rule('/create', 'create', self.create_usage, methods=['GET', 'POST'])
        self.blueprint.add_url_rule('/<int:usage_id>', 'detail', self.usage_detail, methods=['GET'])
        self.blueprint.add_url_rule('/<int:usage_id>/edit', 'edit', self.edit_usage, methods=['GET', 'POST'])
        self.blueprint.add_url_rule('/<int:usage_id>/delete', 'delete', self.delete_usage, methods=['POST'])
        self.blueprint.add_url_rule('/my', 'my_usages', self.my_usages, methods=['GET'])
        self.blueprint.add_url_rule('/department', 'department_usages', self.department_usages, methods=['GET'])
        self.blueprint.add_url_rule('/statistics', 'statistics', self.usage_statistics, methods=['GET'])
    
    def list_usages(self):
        """使用记录列表"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            query_params = self.get_query_params()
            page = int(query_params.get('page', 1))
            per_page = int(query_params.get('per_page', 20))
            material_id = query_params.get('material_id')
            department_id = query_params.get('department_id')
            
            # 获取使用记录列表
            if self.is_admin():
                usages = self.usage_service.get_all_usages()
            else:
                # 普通用户只能看到自己部门的使用记录
                user_dept_id = session.get('department_id')
                if user_dept_id:
                    usages = self.usage_service.get_usages_by_department(user_dept_id)
                else:
                    usages = []
            
            # 过滤
            if material_id:
                usages = [u for u in usages if u.material_id == int(material_id)]
            if department_id and self.is_admin():
                usages = [u for u in usages if u.department_id == int(department_id)]
            
            # 分页
            paginated_data = self.paginate_data([u.to_dict() for u in usages], page, per_page)
            
            if request.is_json:
                return paginated_data
            else:
                materials = self.material_service.get_consumable_materials()
                departments = self.department_service.get_active_departments() if self.is_admin() else []
                return self.render_page('usage/list.html', 
                                      usages=paginated_data['data'],
                                      pagination=paginated_data['pagination'],
                                      materials=materials,
                                      departments=departments,
                                      current_material_id=material_id,
                                      current_department_id=department_id)
                                      
        except Exception as e:
            return self.handle_exception(e, "获取使用记录列表")
    
    def create_usage(self):
        """创建使用记录"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        if request.method == 'GET':
            materials = self.material_service.get_consumable_materials()
            return self.render_page('usage/create.html', materials=materials)
        
        try:
            data = self.get_request_data()
            
            # 调用使用服务创建记录
            result = self.usage_service.create_usage_record(data, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "使用记录创建成功",
                url_for('usage.list') if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "创建使用记录")
    
    def usage_detail(self, usage_id):
        """使用记录详情"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            usage = self.usage_service.get_usage_by_id(usage_id)
            
            if not usage:
                if request.is_json:
                    return self.error_response("使用记录不存在", 404)
                else:
                    flash("使用记录不存在", 'error')
                    return redirect(url_for('usage.list'))
            
            # 权限检查：普通用户只能查看自己部门的记录
            if not self.is_admin():
                user_dept_id = session.get('department_id')
                if usage.department_id != user_dept_id:
                    if request.is_json:
                        return self.error_response("权限不足", 403)
                    else:
                        flash("权限不足", 'error')
                        return redirect(url_for('usage.list'))
            
            if request.is_json:
                return self.success_response("获取成功", usage.to_dict())
            else:
                return self.render_page('usage/detail.html', usage=usage)
                
        except Exception as e:
            return self.handle_exception(e, "获取使用记录详情")
    
    def edit_usage(self, usage_id):
        """编辑使用记录"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        if request.method == 'GET':
            usage = self.usage_service.get_usage_by_id(usage_id)
            if not usage:
                flash("使用记录不存在", 'error')
                return redirect(url_for('usage.list'))
            
            # 权限检查：普通用户只能编辑自己部门的记录
            if not self.is_admin():
                user_dept_id = session.get('department_id')
                if usage.department_id != user_dept_id:
                    flash("权限不足", 'error')
                    return redirect(url_for('usage.list'))
            
            materials = self.material_service.get_consumable_materials()
            return self.render_page('usage/edit.html', usage=usage, materials=materials)
        
        try:
            data = self.get_request_data()
            
            # 调用使用服务更新记录
            result = self.usage_service.update_usage_record(usage_id, data, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "使用记录更新成功",
                url_for('usage.detail', usage_id=usage_id) if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "更新使用记录")
    
    def delete_usage(self, usage_id):
        """删除使用记录"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            # 调用使用服务删除记录
            result = self.usage_service.delete_usage_record(usage_id, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "使用记录删除成功",
                url_for('usage.list') if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "删除使用记录")
    
    def my_usages(self):
        """我的使用记录"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            usages = self.usage_service.get_usages_by_user(self.get_current_user_id())
            
            if request.is_json:
                return self.success_response("获取成功", [u.to_dict() for u in usages])
            else:
                return self.render_page('usage/my_usages.html', usages=usages)
                
        except Exception as e:
            return self.handle_exception(e, "获取我的使用记录")
    
    def department_usages(self):
        """部门使用记录"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            user_dept_id = session.get('department_id')
            if not user_dept_id:
                if request.is_json:
                    return self.error_response("用户未分配部门")
                else:
                    flash("用户未分配部门", 'error')
                    return redirect(url_for('usage.list'))
            
            usages = self.usage_service.get_usages_by_department(user_dept_id)
            
            if request.is_json:
                return self.success_response("获取成功", [u.to_dict() for u in usages])
            else:
                return self.render_page('usage/department_usages.html', usages=usages)
                
        except Exception as e:
            return self.handle_exception(e, "获取部门使用记录")
    
    def usage_statistics(self):
        """使用统计"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            query_params = self.get_query_params()
            start_date, end_date = self.parse_date_range(
                query_params.get('start_date'),
                query_params.get('end_date')
            )
            
            # 获取使用统计数据
            statistics = self.usage_service.get_usage_statistics(start_date, end_date)
            
            if request.is_json:
                return self.success_response("获取成功", statistics)
            else:
                return self.render_page('usage/statistics.html', 
                                      statistics=statistics,
                                      start_date=start_date,
                                      end_date=end_date)
                
        except Exception as e:
            return self.handle_exception(e, "获取使用统计")
