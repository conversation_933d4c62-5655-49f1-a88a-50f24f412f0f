# -*- coding: utf-8 -*-
"""
部门服务
"""

from typing import Optional, List, Dict, Any
from .base_service import BaseService
from dao.department_dao import DepartmentDAO
from dao.user_dao import UserDAO
from models.department_model import Department

class DepartmentService(BaseService):
    """部门业务逻辑服务"""
    
    def __init__(self):
        super().__init__()
        self.department_dao = DepartmentDAO()
        self.user_dao = UserDAO()
    
    def get_all_departments(self) -> List[Department]:
        """获取所有部门"""
        try:
            return self.department_dao.find_all_with_manager()
        except Exception as e:
            self.logger.error(f"获取部门列表失败: {e}")
            return []
    
    def get_active_departments(self) -> List[Department]:
        """获取激活的部门"""
        try:
            return self.department_dao.find_all_active()
        except Exception as e:
            self.logger.error(f"获取激活部门失败: {e}")
            return []
    
    def get_department_by_id(self, department_id: int) -> Optional[Department]:
        """根据ID获取部门"""
        try:
            return self.department_dao.find_by_id(department_id)
        except Exception as e:
            self.logger.error(f"获取部门失败: {e}")
            return None
    
    def create_department(self, department_data: Dict[str, Any], operator_id: int) -> Dict[str, Any]:
        """创建部门"""
        try:
            # 验证必填字段
            required_fields = ['name']
            error = self.validate_required_fields(department_data, required_fields)
            if error:
                return self.create_error_response(error)
            
            # 验证字段格式
            error = self.validate_string_length(department_data['name'], '部门名称', 2, 100)
            if error:
                return self.create_error_response(error)
            
            # 检查部门名称是否已存在
            if self.department_dao.check_name_exists(department_data['name']):
                return self.create_error_response("部门名称已存在")
            
            # 验证负责人（如果提供）
            if department_data.get('manager_id'):
                manager = self.user_dao.find_by_id(department_data['manager_id'])
                if not manager:
                    return self.create_error_response("指定的负责人不存在")
                if not manager.is_active():
                    return self.create_error_response("指定的负责人已停用")
            
            # 设置默认状态
            if 'status' not in department_data:
                department_data['status'] = 'active'
            
            # 创建部门对象
            department = Department(**department_data)
            
            # 验证部门数据
            if not department.validate():
                return self.create_error_response("部门数据验证失败")
            
            # 保存部门
            created_department = self.department_dao.insert(department)
            
            self.log_operation("创建部门", operator_id, f"部门: {created_department.name}")
            
            return self.create_success_response("部门创建成功", created_department.to_dict())
            
        except Exception as e:
            return self.handle_service_error(e, "创建部门")
    
    def update_department(self, department_id: int, department_data: Dict[str, Any], operator_id: int) -> Dict[str, Any]:
        """更新部门"""
        try:
            # 获取现有部门
            department = self.department_dao.find_by_id(department_id)
            if not department:
                return self.create_error_response("部门不存在")
            
            # 验证必填字段
            required_fields = ['name']
            error = self.validate_required_fields(department_data, required_fields)
            if error:
                return self.create_error_response(error)
            
            # 验证字段格式
            error = self.validate_string_length(department_data['name'], '部门名称', 2, 100)
            if error:
                return self.create_error_response(error)
            
            # 检查部门名称是否已存在（排除当前部门）
            if self.department_dao.check_name_exists(department_data['name'], department_id):
                return self.create_error_response("部门名称已存在")
            
            # 验证负责人（如果提供）
            if department_data.get('manager_id'):
                manager = self.user_dao.find_by_id(department_data['manager_id'])
                if not manager:
                    return self.create_error_response("指定的负责人不存在")
            
            # 更新部门属性
            department.update(**department_data)
            
            # 验证部门数据
            if not department.validate():
                return self.create_error_response("部门数据验证失败")
            
            # 保存部门
            updated_department = self.department_dao.update(department)
            
            self.log_operation("更新部门", operator_id, f"部门: {updated_department.name}")
            
            return self.create_success_response("部门更新成功", updated_department.to_dict())
            
        except Exception as e:
            return self.handle_service_error(e, "更新部门")
    
    def delete_department(self, department_id: int, operator_id: int) -> Dict[str, Any]:
        """删除部门"""
        try:
            # 获取部门
            department = self.department_dao.find_by_id(department_id)
            if not department:
                return self.create_error_response("部门不存在")
            
            # 检查是否有用户属于该部门
            users = self.user_dao.find_by_department(department_id)
            if users:
                return self.create_error_response("该部门下还有用户，无法删除")
            
            # 软删除：更新状态为inactive
            department.status = 'inactive'
            self.department_dao.update(department)
            
            self.log_operation("删除部门", operator_id, f"部门: {department.name}")
            
            return self.create_success_response("部门删除成功")
            
        except Exception as e:
            return self.handle_service_error(e, "删除部门")
