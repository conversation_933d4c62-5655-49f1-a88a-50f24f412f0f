#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
"""

import pymysql
import os

def init_database():
    """初始化数据库"""
    
    # 数据库连接配置
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'qyf20031211',
        'charset': 'utf8mb4'
    }
    
    print("正在连接MySQL服务器...")
    
    try:
        # 连接MySQL服务器
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        # 创建数据库
        print("正在创建数据库 'goods'...")
        cursor.execute("CREATE DATABASE IF NOT EXISTS goods CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        cursor.execute("USE goods")
        
        # 读取SQL文件
        print("正在读取数据库架构文件...")
        with open('database_schema.sql', 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL语句
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        print(f"正在执行 {len(sql_statements)} 条SQL语句...")
        
        # 执行每条SQL语句
        for i, statement in enumerate(sql_statements, 1):
            if statement:
                try:
                    cursor.execute(statement)
                    print(f"✓ 执行语句 {i}/{len(sql_statements)}")
                except Exception as e:
                    print(f"✗ 执行语句 {i} 失败: {e}")
                    print(f"语句内容: {statement[:100]}...")
        
        # 提交事务
        connection.commit()
        print("✓ 数据库初始化完成！")
        
        # 验证表是否创建成功
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print(f"\n创建的表: {[table[0] for table in tables]}")
        
        # 检查用户表数据
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"用户表记录数: {user_count}")
        
        if user_count > 0:
            cursor.execute("SELECT username, role FROM users")
            users = cursor.fetchall()
            print("默认用户:")
            for user in users:
                print(f"  - {user[0]} ({user[1]})")
        
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        return False
    
    finally:
        if 'connection' in locals():
            connection.close()
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("金融企业单位物资管理系统 - 数据库初始化")
    print("=" * 60)
    
    if init_database():
        print("\n" + "=" * 60)
        print("数据库初始化成功！")
        print("现在可以启动系统了: python run.py")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("数据库初始化失败！请检查MySQL服务和配置。")
        print("=" * 60)
