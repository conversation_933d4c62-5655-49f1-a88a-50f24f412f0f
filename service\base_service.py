# -*- coding: utf-8 -*-
"""
基础服务类
"""

from typing import Dict, Any, Optional
import logging

class BaseService:
    """基础服务抽象类"""
    
    def __init__(self):
        """初始化服务"""
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def validate_required_fields(self, data: Dict[str, Any], required_fields: list) -> Optional[str]:
        """验证必填字段"""
        for field in required_fields:
            if field not in data or not data[field]:
                return f"字段 '{field}' 是必填的"
        return None
    
    def validate_positive_number(self, value: Any, field_name: str) -> Optional[str]:
        """验证正数"""
        try:
            num = float(value)
            if num <= 0:
                return f"字段 '{field_name}' 必须是正数"
        except (ValueError, TypeError):
            return f"字段 '{field_name}' 必须是有效的数字"
        return None
    
    def validate_non_negative_number(self, value: Any, field_name: str) -> Optional[str]:
        """验证非负数"""
        try:
            num = float(value)
            if num < 0:
                return f"字段 '{field_name}' 不能是负数"
        except (ValueError, TypeError):
            return f"字段 '{field_name}' 必须是有效的数字"
        return None
    
    def validate_string_length(self, value: str, field_name: str, min_length: int = 1, max_length: int = 255) -> Optional[str]:
        """验证字符串长度"""
        if not isinstance(value, str):
            return f"字段 '{field_name}' 必须是字符串"
        
        if len(value) < min_length:
            return f"字段 '{field_name}' 长度不能少于 {min_length} 个字符"
        
        if len(value) > max_length:
            return f"字段 '{field_name}' 长度不能超过 {max_length} 个字符"
        
        return None
    
    def validate_choice(self, value: Any, field_name: str, choices: list) -> Optional[str]:
        """验证选择项"""
        if value not in choices:
            return f"字段 '{field_name}' 必须是以下值之一: {', '.join(map(str, choices))}"
        return None
    
    def log_operation(self, operation: str, user_id: int, details: str = None):
        """记录操作日志"""
        log_message = f"用户 {user_id} 执行操作: {operation}"
        if details:
            log_message += f" - {details}"
        self.logger.info(log_message)
    
    def handle_service_error(self, error: Exception, operation: str) -> Dict[str, Any]:
        """处理服务错误"""
        error_message = f"执行操作 '{operation}' 时发生错误: {str(error)}"
        self.logger.error(error_message, exc_info=True)
        
        return {
            'success': False,
            'error': '操作失败，请稍后重试',
            'details': str(error) if self.logger.isEnabledFor(logging.DEBUG) else None
        }
    
    def create_success_response(self, message: str = "操作成功", data: Any = None) -> Dict[str, Any]:
        """创建成功响应"""
        response = {
            'success': True,
            'message': message
        }
        if data is not None:
            response['data'] = data
        return response
    
    def create_error_response(self, message: str, details: str = None) -> Dict[str, Any]:
        """创建错误响应"""
        response = {
            'success': False,
            'error': message
        }
        if details:
            response['details'] = details
        return response
