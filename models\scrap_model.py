# -*- coding: utf-8 -*-
"""
物资报废模型
"""

from datetime import datetime
from typing import Optional
from .base_model import BaseModel

class MaterialScrap(BaseModel):
    """物资报废模型类"""
    
    def __init__(self, **kwargs):
        # 基本属性
        self.id: Optional[int] = kwargs.get('id')
        self.material_id: int = kwargs.get('material_id')
        self.material_name: Optional[str] = kwargs.get('material_name')
        self.department_id: int = kwargs.get('department_id')
        self.department_name: Optional[str] = kwargs.get('department_name')
        self.quantity: int = kwargs.get('quantity', 1)
        self.applicant_id: int = kwargs.get('applicant_id')
        self.applicant_name: Optional[str] = kwargs.get('applicant_name')
        self.application_date: datetime = kwargs.get('application_date', datetime.now())
        self.reason: str = kwargs.get('reason', '')
        self.scrap_type: str = kwargs.get('scrap_type', 'damage')  # damage, obsolete, loss
        self.estimated_value: Optional[float] = kwargs.get('estimated_value')
        self.status: str = kwargs.get('status', 'pending')  # pending, approved, rejected, processed
        self.approver_id: Optional[int] = kwargs.get('approver_id')
        self.approver_name: Optional[str] = kwargs.get('approver_name')
        self.approval_date: Optional[datetime] = kwargs.get('approval_date')
        self.approval_comment: Optional[str] = kwargs.get('approval_comment')
        self.processor_id: Optional[int] = kwargs.get('processor_id')
        self.processor_name: Optional[str] = kwargs.get('processor_name')
        self.process_date: Optional[datetime] = kwargs.get('process_date')
        self.disposal_method: Optional[str] = kwargs.get('disposal_method')  # recycle, destroy, sell
        self.created_at: Optional[datetime] = kwargs.get('created_at')
        self.updated_at: Optional[datetime] = kwargs.get('updated_at')
        
        super().__init__(**kwargs)
    
    def is_pending(self) -> bool:
        """判断是否待审批"""
        return self.status == 'pending'
    
    def is_approved(self) -> bool:
        """判断是否已审批"""
        return self.status == 'approved'
    
    def is_rejected(self) -> bool:
        """判断是否已拒绝"""
        return self.status == 'rejected'
    
    def is_processed(self) -> bool:
        """判断是否已处理"""
        return self.status == 'processed'
    
    def can_approve(self) -> bool:
        """判断是否可以审批"""
        return self.status == 'pending'
    
    def can_process(self) -> bool:
        """判断是否可以处理"""
        return self.status == 'approved'
    
    def validate(self) -> bool:
        """验证报废数据"""
        if not self.material_id or not self.department_id or not self.applicant_id:
            return False
        if self.quantity <= 0:
            return False
        if not self.reason:
            return False
        if self.scrap_type not in ['damage', 'obsolete', 'loss']:
            return False
        if self.status not in ['pending', 'approved', 'rejected', 'processed']:
            return False
        return True
    
    def get_scrap_type_display(self) -> str:
        """获取报废类型显示名称"""
        type_map = {
            'damage': '损坏',
            'obsolete': '过时',
            'loss': '丢失'
        }
        return type_map.get(self.scrap_type, self.scrap_type)
    
    def get_status_display(self) -> str:
        """获取状态显示名称"""
        status_map = {
            'pending': '待审批',
            'approved': '已审批',
            'rejected': '已拒绝',
            'processed': '已处理'
        }
        return status_map.get(self.status, self.status)
    
    def get_disposal_method_display(self) -> str:
        """获取处置方式显示名称"""
        method_map = {
            'recycle': '回收',
            'destroy': '销毁',
            'sell': '出售'
        }
        return method_map.get(self.disposal_method, self.disposal_method) if self.disposal_method else ''
