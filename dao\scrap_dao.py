# -*- coding: utf-8 -*-
"""
物资报废DAO
"""

from typing import List
from datetime import datetime
from .base_dao import BaseDAO
from models.scrap_model import MaterialScrap

class ScrapDAO(BaseDAO):
    """物资报废数据访问对象"""
    
    def __init__(self):
        super().__init__(MaterialScrap, 'material_scraps')
    
    def insert(self, scrap: MaterialScrap) -> MaterialScrap:
        """插入新报废记录"""
        scrap.created_at = datetime.now()
        scrap.updated_at = datetime.now()
        
        data = {
            'material_id': scrap.material_id,
            'department_id': scrap.department_id,
            'quantity': scrap.quantity,
            'applicant_id': scrap.applicant_id,
            'application_date': scrap.application_date,
            'reason': scrap.reason,
            'scrap_type': scrap.scrap_type,
            'estimated_value': scrap.estimated_value,
            'status': scrap.status,
            'created_at': scrap.created_at,
            'updated_at': scrap.updated_at
        }
        
        query, params = self.build_insert_query(data)
        scrap.id = self.db.execute_insert(query, params)
        return scrap
    
    def update(self, scrap: MaterialScrap) -> MaterialScrap:
        """更新报废记录"""
        scrap.updated_at = datetime.now()
        
        data = {
            'material_id': scrap.material_id,
            'department_id': scrap.department_id,
            'quantity': scrap.quantity,
            'applicant_id': scrap.applicant_id,
            'application_date': scrap.application_date,
            'reason': scrap.reason,
            'scrap_type': scrap.scrap_type,
            'estimated_value': scrap.estimated_value,
            'status': scrap.status,
            'approver_id': scrap.approver_id,
            'approval_date': scrap.approval_date,
            'approval_comment': scrap.approval_comment,
            'processor_id': scrap.processor_id,
            'process_date': scrap.process_date,
            'disposal_method': scrap.disposal_method,
            'updated_at': scrap.updated_at
        }
        
        query, params = self.build_update_query(data, scrap.id)
        self.db.execute_update(query, params)
        return scrap
    
    def find_all_with_details(self) -> List[MaterialScrap]:
        """查找所有报废记录（包含详细信息）"""
        query = """
            SELECT 
                ms.*,
                m.name as material_name,
                d.name as department_name,
                u1.real_name as applicant_name,
                u2.real_name as approver_name,
                u3.real_name as processor_name
            FROM material_scraps ms
            LEFT JOIN materials m ON ms.material_id = m.id
            LEFT JOIN departments d ON ms.department_id = d.id
            LEFT JOIN users u1 ON ms.applicant_id = u1.id
            LEFT JOIN users u2 ON ms.approver_id = u2.id
            LEFT JOIN users u3 ON ms.processor_id = u3.id
            ORDER BY ms.application_date DESC
        """
        results = self.db.execute_query(query, fetch_all=True)
        return [MaterialScrap(**row) for row in results] if results else []
    
    def find_by_status(self, status: str) -> List[MaterialScrap]:
        """根据状态查找报废记录"""
        query = """
            SELECT 
                ms.*,
                m.name as material_name,
                d.name as department_name,
                u1.real_name as applicant_name,
                u2.real_name as approver_name,
                u3.real_name as processor_name
            FROM material_scraps ms
            LEFT JOIN materials m ON ms.material_id = m.id
            LEFT JOIN departments d ON ms.department_id = d.id
            LEFT JOIN users u1 ON ms.applicant_id = u1.id
            LEFT JOIN users u2 ON ms.approver_id = u2.id
            LEFT JOIN users u3 ON ms.processor_id = u3.id
            WHERE ms.status = %s
            ORDER BY ms.application_date DESC
        """
        results = self.db.execute_query(query, (status,), fetch_all=True)
        return [MaterialScrap(**row) for row in results] if results else []
    
    def approve_scrap(self, scrap_id: int, approver_id: int, comment: str = None) -> bool:
        """审批报废申请"""
        query = """
            UPDATE material_scraps 
            SET status = 'approved', approver_id = %s, approval_date = %s, 
                approval_comment = %s, updated_at = %s 
            WHERE id = %s AND status = 'pending'
        """
        result = self.db.execute_update(query, (
            approver_id, datetime.now(), comment, datetime.now(), scrap_id
        ))
        return result > 0
    
    def reject_scrap(self, scrap_id: int, approver_id: int, comment: str = None) -> bool:
        """拒绝报废申请"""
        query = """
            UPDATE material_scraps 
            SET status = 'rejected', approver_id = %s, approval_date = %s, 
                approval_comment = %s, updated_at = %s 
            WHERE id = %s AND status = 'pending'
        """
        result = self.db.execute_update(query, (
            approver_id, datetime.now(), comment, datetime.now(), scrap_id
        ))
        return result > 0
    
    def process_scrap(self, scrap_id: int, processor_id: int, disposal_method: str) -> bool:
        """处理报废物资"""
        query = """
            UPDATE material_scraps 
            SET status = 'processed', processor_id = %s, process_date = %s, 
                disposal_method = %s, updated_at = %s 
            WHERE id = %s AND status = 'approved'
        """
        result = self.db.execute_update(query, (
            processor_id, datetime.now(), disposal_method, datetime.now(), scrap_id
        ))
        return result > 0
