# -*- coding: utf-8 -*-
"""
供应商服务
"""

from typing import Optional, List, Dict, Any
from .base_service import BaseService
from dao.supplier_dao import SupplierDAO
from models.supplier_model import Supplier

class SupplierService(BaseService):
    """供应商业务逻辑服务"""
    
    def __init__(self):
        super().__init__()
        self.supplier_dao = SupplierDAO()
    
    def get_all_suppliers(self) -> List[Supplier]:
        """获取所有供应商"""
        try:
            return self.supplier_dao.find_all()
        except Exception as e:
            self.logger.error(f"获取供应商列表失败: {e}")
            return []
    
    def get_active_suppliers(self) -> List[Supplier]:
        """获取激活的供应商"""
        try:
            return self.supplier_dao.find_all_active()
        except Exception as e:
            self.logger.error(f"获取激活供应商失败: {e}")
            return []
    
    def get_supplier_by_id(self, supplier_id: int) -> Optional[Supplier]:
        """根据ID获取供应商"""
        try:
            return self.supplier_dao.find_by_id(supplier_id)
        except Exception as e:
            self.logger.error(f"获取供应商失败: {e}")
            return None
    
    def create_supplier(self, supplier_data: Dict[str, Any], operator_id: int) -> Dict[str, Any]:
        """创建供应商"""
        try:
            # 验证必填字段
            required_fields = ['name', 'contact_person', 'phone']
            error = self.validate_required_fields(supplier_data, required_fields)
            if error:
                return self.create_error_response(error)
            
            # 验证字段格式
            error = self.validate_string_length(supplier_data['name'], '供应商名称', 2, 100)
            if error:
                return self.create_error_response(error)
            
            error = self.validate_string_length(supplier_data['contact_person'], '联系人', 2, 50)
            if error:
                return self.create_error_response(error)
            
            error = self.validate_string_length(supplier_data['phone'], '联系电话', 10, 20)
            if error:
                return self.create_error_response(error)
            
            # 验证评级（如果提供）
            if supplier_data.get('rating'):
                error = self.validate_choice(supplier_data['rating'], '评级', ['A', 'B', 'C', 'D'])
                if error:
                    return self.create_error_response(error)
            
            # 检查供应商名称是否已存在
            if self.supplier_dao.check_name_exists(supplier_data['name']):
                return self.create_error_response("供应商名称已存在")
            
            # 设置默认值
            if 'status' not in supplier_data:
                supplier_data['status'] = 'active'
            if 'rating' not in supplier_data:
                supplier_data['rating'] = 'C'
            
            # 创建供应商对象
            supplier = Supplier(**supplier_data)
            
            # 验证供应商数据
            if not supplier.validate():
                return self.create_error_response("供应商数据验证失败")
            
            # 保存供应商
            created_supplier = self.supplier_dao.insert(supplier)
            
            self.log_operation("创建供应商", operator_id, f"供应商: {created_supplier.name}")
            
            return self.create_success_response("供应商创建成功", created_supplier.to_dict())
            
        except Exception as e:
            return self.handle_service_error(e, "创建供应商")
    
    def update_supplier(self, supplier_id: int, supplier_data: Dict[str, Any], operator_id: int) -> Dict[str, Any]:
        """更新供应商"""
        try:
            # 获取现有供应商
            supplier = self.supplier_dao.find_by_id(supplier_id)
            if not supplier:
                return self.create_error_response("供应商不存在")
            
            # 验证必填字段
            required_fields = ['name', 'contact_person', 'phone']
            error = self.validate_required_fields(supplier_data, required_fields)
            if error:
                return self.create_error_response(error)
            
            # 验证字段格式
            error = self.validate_string_length(supplier_data['name'], '供应商名称', 2, 100)
            if error:
                return self.create_error_response(error)
            
            error = self.validate_string_length(supplier_data['contact_person'], '联系人', 2, 50)
            if error:
                return self.create_error_response(error)
            
            error = self.validate_string_length(supplier_data['phone'], '联系电话', 10, 20)
            if error:
                return self.create_error_response(error)
            
            # 验证评级（如果提供）
            if supplier_data.get('rating'):
                error = self.validate_choice(supplier_data['rating'], '评级', ['A', 'B', 'C', 'D'])
                if error:
                    return self.create_error_response(error)
            
            # 检查供应商名称是否已存在（排除当前供应商）
            if self.supplier_dao.check_name_exists(supplier_data['name'], supplier_id):
                return self.create_error_response("供应商名称已存在")
            
            # 更新供应商属性
            supplier.update(**supplier_data)
            
            # 验证供应商数据
            if not supplier.validate():
                return self.create_error_response("供应商数据验证失败")
            
            # 保存供应商
            updated_supplier = self.supplier_dao.update(supplier)
            
            self.log_operation("更新供应商", operator_id, f"供应商: {updated_supplier.name}")
            
            return self.create_success_response("供应商更新成功", updated_supplier.to_dict())
            
        except Exception as e:
            return self.handle_service_error(e, "更新供应商")
    
    def delete_supplier(self, supplier_id: int, operator_id: int) -> Dict[str, Any]:
        """删除供应商"""
        try:
            # 获取供应商
            supplier = self.supplier_dao.find_by_id(supplier_id)
            if not supplier:
                return self.create_error_response("供应商不存在")
            
            # 检查是否有物资关联该供应商
            # 这里可以添加检查逻辑
            
            # 软删除：更新状态为inactive
            supplier.status = 'inactive'
            self.supplier_dao.update(supplier)
            
            self.log_operation("删除供应商", operator_id, f"供应商: {supplier.name}")
            
            return self.create_success_response("供应商删除成功")
            
        except Exception as e:
            return self.handle_service_error(e, "删除供应商")
    
    def search_suppliers(self, keyword: str) -> List[Supplier]:
        """搜索供应商"""
        try:
            if not keyword or len(keyword.strip()) < 2:
                return []
            return self.supplier_dao.search_suppliers(keyword.strip())
        except Exception as e:
            self.logger.error(f"搜索供应商失败: {e}")
            return []
    
    def get_suppliers_by_rating(self, rating: str) -> List[Supplier]:
        """根据评级获取供应商"""
        try:
            return self.supplier_dao.find_by_rating(rating)
        except Exception as e:
            self.logger.error(f"获取供应商失败: {e}")
            return []
    
    def update_supplier_rating(self, supplier_id: int, rating: str, operator_id: int) -> Dict[str, Any]:
        """更新供应商评级"""
        try:
            # 验证评级
            error = self.validate_choice(rating, '评级', ['A', 'B', 'C', 'D'])
            if error:
                return self.create_error_response(error)
            
            # 获取供应商
            supplier = self.supplier_dao.find_by_id(supplier_id)
            if not supplier:
                return self.create_error_response("供应商不存在")
            
            # 更新评级
            success = self.supplier_dao.update_rating(supplier_id, rating)
            
            if success:
                self.log_operation("更新供应商评级", operator_id, 
                                 f"供应商: {supplier.name}, 新评级: {rating}")
                return self.create_success_response("供应商评级更新成功")
            else:
                return self.create_error_response("评级更新失败")
                
        except Exception as e:
            return self.handle_service_error(e, "更新供应商评级")
