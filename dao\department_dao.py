# -*- coding: utf-8 -*-
"""
部门DAO
"""

from typing import Optional, List
from datetime import datetime
from .base_dao import BaseDAO
from models.department_model import Department

class DepartmentDAO(BaseDAO):
    """部门数据访问对象"""
    
    def __init__(self):
        super().__init__(Department, 'departments')
    
    def insert(self, department: Department) -> Department:
        """插入新部门"""
        department.created_at = datetime.now()
        department.updated_at = datetime.now()
        
        data = {
            'name': department.name,
            'description': department.description,
            'created_at': department.created_at,
            'updated_at': department.updated_at
        }
        
        query, params = self.build_insert_query(data)
        department.id = self.db.execute_insert(query, params)
        return department
    
    def update(self, department: Department) -> Department:
        """更新部门"""
        department.updated_at = datetime.now()
        
        data = {
            'name': department.name,
            'description': department.description,
            'updated_at': department.updated_at
        }
        
        query, params = self.build_update_query(data, department.id)
        self.db.execute_update(query, params)
        return department
    
    def find_all_active(self) -> List[Department]:
        """查找所有激活的部门"""
        query = """
            SELECT d.*
            FROM departments d
            ORDER BY d.name
        """
        results = self.db.execute_query(query, fetch_all=True)
        return [Department(**row) for row in results] if results else []
    
    def find_all_with_manager(self) -> List[Department]:
        """查找所有部门"""
        query = """
            SELECT d.*
            FROM departments d
            ORDER BY d.created_at DESC
        """
        results = self.db.execute_query(query, fetch_all=True)
        return [Department(**row) for row in results] if results else []
    
    def find_by_manager(self, manager_id: int) -> Optional[Department]:
        """根据负责人查找部门（暂时不支持，因为表中没有manager_id字段）"""
        # 暂时返回None，因为数据库表中没有manager_id字段
        return None
    
    def check_name_exists(self, name: str, exclude_id: Optional[int] = None) -> bool:
        """检查部门名称是否存在"""
        query = "SELECT COUNT(*) as count FROM departments WHERE name = %s"
        params = [name]
        
        if exclude_id:
            query += " AND id != %s"
            params.append(exclude_id)
        
        result = self.db.execute_query(query, tuple(params), fetch_one=True)
        return result['count'] > 0 if result else False
