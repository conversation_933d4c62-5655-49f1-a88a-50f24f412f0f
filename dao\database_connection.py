# -*- coding: utf-8 -*-
"""
数据库连接管理
"""

import pymysql
import logging
from typing import Optional, Dict, Any, List, Tuple
from contextlib import contextmanager

class DatabaseConnection:
    """数据库连接管理类"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化数据库连接配置"""
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(
                host=self.config['host'],
                user=self.config['user'],
                password=self.config['password'],
                database=self.config['database'],
                charset=self.config.get('charset', 'utf8mb4'),
                autocommit=self.config.get('autocommit', False),
                cursorclass=pymysql.cursors.DictCursor
            )
            return connection
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    @contextmanager
    def get_cursor(self, autocommit=False):
        """获取数据库游标（上下文管理器）"""
        connection = None
        cursor = None
        try:
            connection = self.get_connection()
            if autocommit:
                connection.autocommit(True)
            cursor = connection.cursor()
            yield cursor
            if not autocommit:
                connection.commit()
        except Exception as e:
            if connection and not autocommit:
                connection.rollback()
            self.logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
    
    def execute_query(self, query: str, params: Optional[Tuple] = None, 
                     fetch_one: bool = False, fetch_all: bool = True) -> Optional[Any]:
        """执行查询语句"""
        try:
            with self.get_cursor(autocommit=True) as cursor:
                cursor.execute(query, params or ())
                
                if fetch_one:
                    return cursor.fetchone()
                elif fetch_all:
                    return cursor.fetchall()
                else:
                    return cursor.rowcount
        except Exception as e:
            self.logger.error(f"查询执行失败: {e}")
            raise
    
    def execute_update(self, query: str, params: Optional[Tuple] = None) -> int:
        """执行更新语句"""
        try:
            with self.get_cursor() as cursor:
                result = cursor.execute(query, params or ())
                return result
        except Exception as e:
            self.logger.error(f"更新执行失败: {e}")
            raise
    
    def execute_insert(self, query: str, params: Optional[Tuple] = None) -> int:
        """执行插入语句，返回插入的ID"""
        try:
            with self.get_cursor() as cursor:
                cursor.execute(query, params or ())
                return cursor.lastrowid
        except Exception as e:
            self.logger.error(f"插入执行失败: {e}")
            raise
    
    def execute_batch(self, query: str, params_list: List[Tuple]) -> int:
        """批量执行语句"""
        try:
            with self.get_cursor() as cursor:
                result = cursor.executemany(query, params_list)
                return result
        except Exception as e:
            self.logger.error(f"批量执行失败: {e}")
            raise

# 全局数据库连接实例
_db_connection = None

def get_db_connection() -> DatabaseConnection:
    """获取全局数据库连接实例"""
    global _db_connection
    if _db_connection is None:
        config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'qyf20031211',
            'database': 'goods',
            'charset': 'utf8mb4',
            'autocommit': False
        }
        _db_connection = DatabaseConnection(config)
    return _db_connection
