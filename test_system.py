#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统功能测试脚本
"""

import requests
import json
import time

def test_system():
    """测试系统基本功能"""
    base_url = "http://127.0.0.1:5000"
    
    print("=" * 60)
    print("金融企业单位物资管理系统 - 功能测试")
    print("=" * 60)
    
    # 测试主页访问
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✓ 主页访问正常")
        else:
            print(f"✗ 主页访问失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"✗ 主页访问失败: {e}")
        return False
    
    # 测试登录页面
    try:
        response = requests.get(f"{base_url}/login", timeout=5)
        if response.status_code == 200:
            print("✓ 登录页面访问正常")
        else:
            print(f"✗ 登录页面访问失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"✗ 登录页面访问失败: {e}")
    
    # 测试静态资源
    try:
        response = requests.get(f"{base_url}/static/css/style.css", timeout=5)
        if response.status_code == 200:
            print("✓ 静态资源访问正常")
        else:
            print("! 静态资源可能不存在（这是正常的）")
    except Exception as e:
        print("! 静态资源访问失败（这是正常的）")
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("请在浏览器中访问: http://127.0.0.1:5000")
    print("默认管理员账户: admin / admin123")
    print("默认员工账户: user1 / user123")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    test_system()
