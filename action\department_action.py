# -*- coding: utf-8 -*-
"""
部门管理控制器
"""

from flask import Blueprint, request, redirect, url_for, flash
from .base_action import BaseAction
from service.department_service import DepartmentService
from service.user_service import UserService

class DepartmentAction(BaseAction):
    """部门管理控制器"""
    
    def __init__(self):
        super().__init__()
        self.department_service = DepartmentService()
        self.user_service = UserService()
        self.blueprint = Blueprint('department', __name__, url_prefix='/department')
        self._register_routes()
    
    def _register_routes(self):
        """注册路由"""
        self.blueprint.add_url_rule('/', 'list', self.list_departments, methods=['GET'])
        self.blueprint.add_url_rule('/create', 'create', self.create_department, methods=['GET', 'POST'])
        self.blueprint.add_url_rule('/<int:department_id>', 'detail', self.department_detail, methods=['GET'])
        self.blueprint.add_url_rule('/<int:department_id>/edit', 'edit', self.edit_department, methods=['GET', 'POST'])
        self.blueprint.add_url_rule('/<int:department_id>/delete', 'delete', self.delete_department, methods=['POST'])
    
    def list_departments(self):
        """部门列表"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            query_params = self.get_query_params()
            page = int(query_params.get('page', 1))
            per_page = int(query_params.get('per_page', 20))
            status = query_params.get('status')
            
            # 获取部门列表
            if self.is_admin():
                departments = self.department_service.get_all_departments()
            else:
                departments = self.department_service.get_active_departments()
            
            # 过滤
            if status:
                departments = [d for d in departments if d.status == status]
            
            # 分页
            paginated_data = self.paginate_data([d.to_dict() for d in departments], page, per_page)
            
            if request.is_json:
                return paginated_data
            else:
                return self.render_page('department/list.html', 
                                      departments=paginated_data['data'],
                                      pagination=paginated_data['pagination'],
                                      current_status=status)
                                      
        except Exception as e:
            return self.handle_exception(e, "获取部门列表")
    
    def create_department(self):
        """创建部门（仅管理员）"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('department.list'))
        
        if request.method == 'GET':
            users = self.user_service.get_active_users()
            return self.render_page('department/create.html', users=users)
        
        try:
            data = self.get_request_data()
            
            # 调用部门服务创建部门
            result = self.department_service.create_department(data, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "部门创建成功",
                url_for('department.list') if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "创建部门")
    
    def department_detail(self, department_id):
        """部门详情"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            department = self.department_service.get_department_by_id(department_id)
            
            if not department:
                if request.is_json:
                    return self.error_response("部门不存在", 404)
                else:
                    flash("部门不存在", 'error')
                    return redirect(url_for('department.list'))
            
            if request.is_json:
                return self.success_response("获取成功", department.to_dict())
            else:
                return self.render_page('department/detail.html', department=department)
                
        except Exception as e:
            return self.handle_exception(e, "获取部门详情")
    
    def edit_department(self, department_id):
        """编辑部门（仅管理员）"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('department.list'))
        
        if request.method == 'GET':
            department = self.department_service.get_department_by_id(department_id)
            if not department:
                flash("部门不存在", 'error')
                return redirect(url_for('department.list'))
            
            users = self.user_service.get_active_users()
            return self.render_page('department/edit.html', department=department, users=users)
        
        try:
            data = self.get_request_data()
            
            # 调用部门服务更新部门
            result = self.department_service.update_department(department_id, data, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "部门更新成功",
                url_for('department.detail', department_id=department_id) if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "更新部门")
    
    def delete_department(self, department_id):
        """删除部门（仅管理员）"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('department.list'))
        
        try:
            # 调用部门服务删除部门
            result = self.department_service.delete_department(department_id, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "部门删除成功",
                url_for('department.list') if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "删除部门")
