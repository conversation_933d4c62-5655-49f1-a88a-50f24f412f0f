# -*- coding: utf-8 -*-
"""
物资报废服务
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from .base_service import BaseService
from dao.scrap_dao import ScrapDAO
from dao.material_dao import MaterialDAO
from dao.department_dao import DepartmentDAO
from models.scrap_model import MaterialScrap

class ScrapService(BaseService):
    """物资报废业务逻辑服务"""
    
    def __init__(self):
        super().__init__()
        self.scrap_dao = ScrapDAO()
        self.material_dao = MaterialDAO()
        self.department_dao = DepartmentDAO()
    
    def get_all_scraps(self) -> List[MaterialScrap]:
        """获取所有报废记录"""
        try:
            return self.scrap_dao.find_all_with_details()
        except Exception as e:
            self.logger.error(f"获取报废记录失败: {e}")
            return []
    
    def get_scrap_by_id(self, scrap_id: int) -> Optional[MaterialScrap]:
        """根据ID获取报废记录"""
        try:
            return self.scrap_dao.find_by_id(scrap_id)
        except Exception as e:
            self.logger.error(f"获取报废记录失败: {e}")
            return None
    
    def create_scrap_request(self, scrap_data: Dict[str, Any], applicant_id: int) -> Dict[str, Any]:
        """创建报废申请"""
        try:
            # 验证必填字段
            required_fields = ['material_id', 'department_id', 'quantity', 'reason', 'scrap_type']
            error = self.validate_required_fields(scrap_data, required_fields)
            if error:
                return self.create_error_response(error)
            
            # 验证数量
            error = self.validate_positive_number(scrap_data['quantity'], '报废数量')
            if error:
                return self.create_error_response(error)
            
            # 验证报废类型
            error = self.validate_choice(scrap_data['scrap_type'], '报废类型', 
                                       ['damage', 'obsolete', 'expired', 'lost', 'other'])
            if error:
                return self.create_error_response(error)
            
            # 验证物资是否存在
            material = self.material_dao.find_by_id(scrap_data['material_id'])
            if not material:
                return self.create_error_response("指定的物资不存在")
            
            # 验证库存是否充足
            if material.quantity < scrap_data['quantity']:
                return self.create_error_response(f"库存不足，当前库存: {material.quantity}")
            
            # 验证部门是否存在
            department = self.department_dao.find_by_id(scrap_data['department_id'])
            if not department:
                return self.create_error_response("指定的部门不存在")
            
            # 验证预估价值（如果提供）
            if scrap_data.get('estimated_value'):
                error = self.validate_non_negative_number(scrap_data['estimated_value'], '预估价值')
                if error:
                    return self.create_error_response(error)
            
            # 创建报废申请
            scrap_data.update({
                'applicant_id': applicant_id,
                'application_date': datetime.now(),
                'status': 'pending'
            })
            
            scrap = MaterialScrap(**scrap_data)
            
            # 验证报废数据
            if not scrap.validate():
                return self.create_error_response("报废申请数据验证失败")
            
            # 保存报废申请
            created_scrap = self.scrap_dao.insert(scrap)
            
            self.log_operation("创建报废申请", applicant_id, 
                             f"物资: {material.name}, 数量: {scrap_data['quantity']}")
            
            return self.create_success_response("报废申请创建成功", created_scrap.to_dict())
            
        except Exception as e:
            return self.handle_service_error(e, "创建报废申请")
    
    def approve_scrap(self, scrap_id: int, approver_id: int, comment: str = None) -> Dict[str, Any]:
        """审批报废申请"""
        try:
            # 获取报废申请
            scrap = self.scrap_dao.find_by_id(scrap_id)
            if not scrap:
                return self.create_error_response("报废申请不存在")
            
            if scrap.status != 'pending':
                return self.create_error_response("报废申请状态不正确，无法审批")
            
            # 再次检查库存
            material = self.material_dao.find_by_id(scrap.material_id)
            if material.quantity < scrap.quantity:
                return self.create_error_response(f"库存不足，当前库存: {material.quantity}")
            
            # 审批通过
            success = self.scrap_dao.approve_scrap(scrap_id, approver_id, comment)
            
            if success:
                self.log_operation("审批报废申请", approver_id, 
                                 f"申请ID: {scrap_id}, 审批通过")
                
                return self.create_success_response("报废申请审批通过")
            else:
                return self.create_error_response("审批失败")
                
        except Exception as e:
            return self.handle_service_error(e, "审批报废申请")
    
    def reject_scrap(self, scrap_id: int, approver_id: int, comment: str = None) -> Dict[str, Any]:
        """拒绝报废申请"""
        try:
            # 获取报废申请
            scrap = self.scrap_dao.find_by_id(scrap_id)
            if not scrap:
                return self.create_error_response("报废申请不存在")
            
            if scrap.status != 'pending':
                return self.create_error_response("报废申请状态不正确，无法拒绝")
            
            # 拒绝申请
            success = self.scrap_dao.reject_scrap(scrap_id, approver_id, comment)
            
            if success:
                self.log_operation("拒绝报废申请", approver_id, 
                                 f"申请ID: {scrap_id}, 拒绝原因: {comment}")
                
                return self.create_success_response("报废申请已拒绝")
            else:
                return self.create_error_response("拒绝失败")
                
        except Exception as e:
            return self.handle_service_error(e, "拒绝报废申请")
    
    def process_scrap(self, scrap_id: int, processor_id: int, disposal_method: str) -> Dict[str, Any]:
        """处理报废物资"""
        try:
            # 获取报废申请
            scrap = self.scrap_dao.find_by_id(scrap_id)
            if not scrap:
                return self.create_error_response("报废申请不存在")
            
            if scrap.status != 'approved':
                return self.create_error_response("报废申请状态不正确，无法处理")
            
            # 验证处理方式
            if not disposal_method or len(disposal_method.strip()) < 2:
                return self.create_error_response("请输入处理方式")
            
            # 处理报废
            success = self.scrap_dao.process_scrap(scrap_id, processor_id, disposal_method.strip())
            
            if success:
                # 减少库存并更新物资状态
                self.material_dao.update_quantity(scrap.material_id, -scrap.quantity)
                
                # 如果是固定资产且全部报废，更新状态为scrapped
                material = self.material_dao.find_by_id(scrap.material_id)
                if material.category == 'fixed_asset' and material.quantity == 0:
                    self.material_dao.update_status(scrap.material_id, 'scrapped')
                
                self.log_operation("处理报废物资", processor_id, 
                                 f"申请ID: {scrap_id}, 处理方式: {disposal_method}")
                
                return self.create_success_response("报废物资处理完成")
            else:
                return self.create_error_response("处理失败")
                
        except Exception as e:
            return self.handle_service_error(e, "处理报废物资")
    
    def get_scraps_by_status(self, status: str) -> List[MaterialScrap]:
        """根据状态获取报废记录"""
        try:
            return self.scrap_dao.find_by_status(status)
        except Exception as e:
            self.logger.error(f"获取报废记录失败: {e}")
            return []
    
    def get_pending_scraps(self) -> List[MaterialScrap]:
        """获取待审批的报废申请"""
        return self.get_scraps_by_status('pending')
    
    def cancel_scrap(self, scrap_id: int, operator_id: int) -> Dict[str, Any]:
        """取消报废申请"""
        try:
            # 获取报废申请
            scrap = self.scrap_dao.find_by_id(scrap_id)
            if not scrap:
                return self.create_error_response("报废申请不存在")
            
            if scrap.status not in ['pending', 'approved']:
                return self.create_error_response("报废申请状态不正确，无法取消")
            
            # 更新状态为取消
            scrap.status = 'cancelled'
            self.scrap_dao.update(scrap)
            
            self.log_operation("取消报废申请", operator_id, f"申请ID: {scrap_id}")
            
            return self.create_success_response("报废申请已取消")
            
        except Exception as e:
            return self.handle_service_error(e, "取消报废申请")
