# -*- coding: utf-8 -*-
"""
基础控制器类
"""

from flask import request, session, jsonify, render_template, redirect, url_for, flash
from functools import wraps
from typing import Dict, Any, Optional, Callable
import logging

class BaseAction:
    """基础控制器抽象类"""
    
    def __init__(self):
        """初始化控制器"""
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def get_current_user_id(self) -> Optional[int]:
        """获取当前登录用户ID"""
        return session.get('user_id')
    
    def get_current_user_role(self) -> Optional[str]:
        """获取当前登录用户角色"""
        return session.get('user_role')
    
    def is_admin(self) -> bool:
        """检查当前用户是否为管理员"""
        return self.get_current_user_role() == 'admin'
    
    def is_logged_in(self) -> bool:
        """检查用户是否已登录"""
        return self.get_current_user_id() is not None
    
    def require_login(self, f: Callable) -> Callable:
        """登录验证装饰器"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not self.is_logged_in():
                if request.is_json:
                    return jsonify({'success': False, 'error': '请先登录'}), 401
                else:
                    flash('请先登录', 'error')
                    return redirect(url_for('auth.login'))
            return f(*args, **kwargs)
        return decorated_function
    
    def require_admin(self, f: Callable) -> Callable:
        """管理员权限验证装饰器"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not self.is_logged_in():
                if request.is_json:
                    return jsonify({'success': False, 'error': '请先登录'}), 401
                else:
                    flash('请先登录', 'error')
                    return redirect(url_for('auth.login'))
            
            if not self.is_admin():
                if request.is_json:
                    return jsonify({'success': False, 'error': '权限不足'}), 403
                else:
                    flash('权限不足', 'error')
                    return redirect(url_for('main.index'))
            
            return f(*args, **kwargs)
        return decorated_function
    
    def get_request_data(self) -> Dict[str, Any]:
        """获取请求数据"""
        if request.is_json:
            return request.get_json() or {}
        else:
            return request.form.to_dict()
    
    def get_query_params(self) -> Dict[str, Any]:
        """获取查询参数"""
        return request.args.to_dict()
    
    def success_response(self, message: str = "操作成功", data: Any = None) -> Dict[str, Any]:
        """创建成功响应"""
        response = {
            'success': True,
            'message': message
        }
        if data is not None:
            response['data'] = data
        return response
    
    def error_response(self, message: str, code: int = 400) -> tuple:
        """创建错误响应"""
        return jsonify({
            'success': False,
            'error': message
        }), code
    
    def render_page(self, template: str, **kwargs) -> str:
        """渲染页面模板"""
        # 添加通用的模板变量
        kwargs.update({
            'current_user_id': self.get_current_user_id(),
            'current_user_role': self.get_current_user_role(),
            'is_admin': self.is_admin()
        })
        return render_template(template, **kwargs)
    
    def handle_service_response(self, service_response: Dict[str, Any], 
                              success_message: str = None, 
                              redirect_url: str = None) -> Any:
        """处理服务层响应"""
        if service_response.get('success'):
            message = success_message or service_response.get('message', '操作成功')
            
            if request.is_json:
                return jsonify(service_response)
            else:
                flash(message, 'success')
                if redirect_url:
                    return redirect(redirect_url)
                else:
                    return self.success_response(message, service_response.get('data'))
        else:
            error_message = service_response.get('error', '操作失败')
            
            if request.is_json:
                return jsonify(service_response), 400
            else:
                flash(error_message, 'error')
                if redirect_url:
                    return redirect(redirect_url)
                else:
                    return self.error_response(error_message)
    
    def validate_required_params(self, data: Dict[str, Any], required_fields: list) -> Optional[str]:
        """验证必需参数"""
        for field in required_fields:
            if field not in data or not data[field]:
                return f"参数 '{field}' 是必需的"
        return None
    
    def log_action(self, action: str, details: str = None):
        """记录控制器操作"""
        user_id = self.get_current_user_id()
        log_message = f"用户 {user_id} 执行操作: {action}"
        if details:
            log_message += f" - {details}"
        self.logger.info(log_message)
    
    def handle_exception(self, e: Exception, operation: str) -> Any:
        """处理异常"""
        error_message = f"执行操作 '{operation}' 时发生错误"
        self.logger.error(f"{error_message}: {str(e)}", exc_info=True)
        
        if request.is_json:
            return self.error_response("系统错误，请稍后重试", 500)
        else:
            flash("系统错误，请稍后重试", 'error')
            return redirect(request.referrer or url_for('main.index'))
    
    def paginate_data(self, data: list, page: int = 1, per_page: int = 20) -> Dict[str, Any]:
        """分页数据"""
        total = len(data)
        start = (page - 1) * per_page
        end = start + per_page
        
        return {
            'data': data[start:end],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': (total + per_page - 1) // per_page,
                'has_prev': page > 1,
                'has_next': end < total
            }
        }
    
    def parse_date_range(self, start_date_str: str = None, end_date_str: str = None) -> tuple:
        """解析日期范围"""
        from datetime import datetime, date
        
        start_date = None
        end_date = None
        
        if start_date_str:
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            except ValueError:
                pass
        
        if end_date_str:
            try:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
            except ValueError:
                pass
        
        return start_date, end_date
