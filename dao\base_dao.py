# -*- coding: utf-8 -*-
"""
基础DAO类
"""

from typing import Optional, List, Dict, Any, Type
from abc import ABC, abstractmethod
from .database_connection import get_db_connection
from models.base_model import BaseModel

class BaseDAO(ABC):
    """基础DAO抽象类"""
    
    def __init__(self, model_class: Type[BaseModel], table_name: str):
        """初始化DAO"""
        self.model_class = model_class
        self.table_name = table_name
        self.db = get_db_connection()
    
    def find_by_id(self, id: int) -> Optional[BaseModel]:
        """根据ID查找记录"""
        query = f"SELECT * FROM {self.table_name} WHERE id = %s"
        result = self.db.execute_query(query, (id,), fetch_one=True)
        return self.model_class(**result) if result else None
    
    def find_all(self, limit: Optional[int] = None, offset: int = 0) -> List[BaseModel]:
        """查找所有记录"""
        query = f"SELECT * FROM {self.table_name}"
        if limit:
            query += f" LIMIT {limit} OFFSET {offset}"
        
        results = self.db.execute_query(query, fetch_all=True)
        return [self.model_class(**row) for row in results] if results else []
    
    def find_by_condition(self, conditions: Dict[str, Any], 
                         limit: Optional[int] = None, offset: int = 0) -> List[BaseModel]:
        """根据条件查找记录"""
        where_clause = " AND ".join([f"{key} = %s" for key in conditions.keys()])
        query = f"SELECT * FROM {self.table_name} WHERE {where_clause}"
        
        if limit:
            query += f" LIMIT {limit} OFFSET {offset}"
        
        params = tuple(conditions.values())
        results = self.db.execute_query(query, params, fetch_all=True)
        return [self.model_class(**row) for row in results] if results else []
    
    def count(self, conditions: Optional[Dict[str, Any]] = None) -> int:
        """统计记录数量"""
        query = f"SELECT COUNT(*) as count FROM {self.table_name}"
        params = None
        
        if conditions:
            where_clause = " AND ".join([f"{key} = %s" for key in conditions.keys()])
            query += f" WHERE {where_clause}"
            params = tuple(conditions.values())
        
        result = self.db.execute_query(query, params, fetch_one=True)
        return result['count'] if result else 0
    
    def save(self, model: BaseModel) -> BaseModel:
        """保存模型（插入或更新）"""
        if hasattr(model, 'id') and model.id:
            return self.update(model)
        else:
            return self.insert(model)
    
    @abstractmethod
    def insert(self, model: BaseModel) -> BaseModel:
        """插入新记录"""
        pass
    
    @abstractmethod
    def update(self, model: BaseModel) -> BaseModel:
        """更新记录"""
        pass
    
    def delete_by_id(self, id: int) -> bool:
        """根据ID删除记录"""
        query = f"DELETE FROM {self.table_name} WHERE id = %s"
        result = self.db.execute_update(query, (id,))
        return result > 0
    
    def delete_by_condition(self, conditions: Dict[str, Any]) -> int:
        """根据条件删除记录"""
        where_clause = " AND ".join([f"{key} = %s" for key in conditions.keys()])
        query = f"DELETE FROM {self.table_name} WHERE {where_clause}"
        params = tuple(conditions.values())
        return self.db.execute_update(query, params)
    
    def execute_custom_query(self, query: str, params: Optional[tuple] = None, 
                           fetch_one: bool = False) -> Any:
        """执行自定义查询"""
        if fetch_one:
            result = self.db.execute_query(query, params, fetch_one=True)
            return self.model_class(**result) if result else None
        else:
            results = self.db.execute_query(query, params, fetch_all=True)
            return [self.model_class(**row) for row in results] if results else []
    
    def execute_raw_query(self, query: str, params: Optional[tuple] = None, 
                         fetch_one: bool = False) -> Any:
        """执行原始查询（返回字典）"""
        return self.db.execute_query(query, params, fetch_one=fetch_one)
    
    def begin_transaction(self):
        """开始事务"""
        return self.db.get_cursor()
    
    def build_insert_query(self, data: Dict[str, Any]) -> tuple:
        """构建插入查询"""
        columns = list(data.keys())
        placeholders = ', '.join(['%s'] * len(columns))
        query = f"INSERT INTO {self.table_name} ({', '.join(columns)}) VALUES ({placeholders})"
        params = tuple(data.values())
        return query, params
    
    def build_update_query(self, data: Dict[str, Any], id: int) -> tuple:
        """构建更新查询"""
        set_clause = ', '.join([f"{key} = %s" for key in data.keys()])
        query = f"UPDATE {self.table_name} SET {set_clause} WHERE id = %s"
        params = tuple(list(data.values()) + [id])
        return query, params
