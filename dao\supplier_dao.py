# -*- coding: utf-8 -*-
"""
供应商DAO
"""

from typing import Optional, List
from datetime import datetime
from .base_dao import BaseDAO
from models.supplier_model import Supplier

class SupplierDAO(BaseDAO):
    """供应商数据访问对象"""
    
    def __init__(self):
        super().__init__(Supplier, 'suppliers')
    
    def insert(self, supplier: Supplier) -> Supplier:
        """插入新供应商"""
        supplier.created_at = datetime.now()
        supplier.updated_at = datetime.now()
        
        data = {
            'name': supplier.name,
            'contact_person': supplier.contact_person,
            'phone': supplier.phone,
            'email': supplier.email,
            'address': supplier.address,
            'business_license': supplier.business_license,
            'tax_number': supplier.tax_number,
            'bank_account': supplier.bank_account,
            'rating': supplier.rating,
            'status': supplier.status,
            'remark': supplier.remark,
            'created_at': supplier.created_at,
            'updated_at': supplier.updated_at
        }
        
        query, params = self.build_insert_query(data)
        supplier.id = self.db.execute_insert(query, params)
        return supplier
    
    def update(self, supplier: Supplier) -> Supplier:
        """更新供应商"""
        supplier.updated_at = datetime.now()
        
        data = {
            'name': supplier.name,
            'contact_person': supplier.contact_person,
            'phone': supplier.phone,
            'email': supplier.email,
            'address': supplier.address,
            'business_license': supplier.business_license,
            'tax_number': supplier.tax_number,
            'bank_account': supplier.bank_account,
            'rating': supplier.rating,
            'status': supplier.status,
            'remark': supplier.remark,
            'updated_at': supplier.updated_at
        }
        
        query, params = self.build_update_query(data, supplier.id)
        self.db.execute_update(query, params)
        return supplier
    
    def find_all_active(self) -> List[Supplier]:
        """查找所有激活的供应商"""
        return self.find_by_condition({'status': 'active'})
    
    def find_by_rating(self, rating: str) -> List[Supplier]:
        """根据评级查找供应商"""
        return self.find_by_condition({'rating': rating})
    
    def search_suppliers(self, keyword: str) -> List[Supplier]:
        """搜索供应商"""
        query = """
            SELECT * FROM suppliers 
            WHERE name LIKE %s OR contact_person LIKE %s OR phone LIKE %s
            ORDER BY name
        """
        search_term = f"%{keyword}%"
        results = self.db.execute_query(query, (search_term, search_term, search_term), fetch_all=True)
        return [Supplier(**row) for row in results] if results else []
    
    def update_rating(self, supplier_id: int, rating: str) -> bool:
        """更新供应商评级"""
        query = "UPDATE suppliers SET rating = %s, updated_at = %s WHERE id = %s"
        result = self.db.execute_update(query, (rating, datetime.now(), supplier_id))
        return result > 0
    
    def check_name_exists(self, name: str, exclude_id: Optional[int] = None) -> bool:
        """检查供应商名称是否存在"""
        query = "SELECT COUNT(*) as count FROM suppliers WHERE name = %s"
        params = [name]
        
        if exclude_id:
            query += " AND id != %s"
            params.append(exclude_id)
        
        result = self.db.execute_query(query, tuple(params), fetch_one=True)
        return result['count'] > 0 if result else False
