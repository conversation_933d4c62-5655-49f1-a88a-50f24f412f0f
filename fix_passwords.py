#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复用户密码脚本
"""

import pymysql
import bcrypt

def fix_passwords():
    """修复用户密码"""
    
    # 数据库连接配置
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'qyf20031211',
        'database': 'goods',
        'charset': 'utf8mb4'
    }
    
    print("正在连接数据库...")
    
    try:
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        # 生成新的密码哈希
        admin_password = "admin123"
        user_password = "user123"
        
        admin_hash = bcrypt.hashpw(admin_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        user_hash = bcrypt.hashpw(user_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        print(f"管理员密码哈希: {admin_hash}")
        print(f"用户密码哈希: {user_hash}")
        
        # 更新管理员密码
        cursor.execute("UPDATE users SET password = %s WHERE username = 'admin'", (admin_hash,))
        print("✓ 更新管理员密码")
        
        # 更新员工密码
        cursor.execute("UPDATE users SET password = %s WHERE username IN ('user1', 'user2')", (user_hash,))
        print("✓ 更新员工密码")
        
        # 提交更改
        connection.commit()
        
        # 验证更新
        cursor.execute("SELECT username, password FROM users")
        users = cursor.fetchall()
        
        print("\n当前用户密码哈希:")
        for user in users:
            print(f"  {user[0]}: {user[1][:50]}...")
        
        # 测试密码验证
        print("\n测试密码验证:")
        
        # 测试管理员密码
        cursor.execute("SELECT password FROM users WHERE username = 'admin'")
        stored_hash = cursor.fetchone()[0]
        if bcrypt.checkpw(admin_password.encode('utf-8'), stored_hash.encode('utf-8')):
            print("✓ 管理员密码验证成功")
        else:
            print("✗ 管理员密码验证失败")
        
        # 测试员工密码
        cursor.execute("SELECT password FROM users WHERE username = 'user1'")
        stored_hash = cursor.fetchone()[0]
        if bcrypt.checkpw(user_password.encode('utf-8'), stored_hash.encode('utf-8')):
            print("✓ 员工密码验证成功")
        else:
            print("✗ 员工密码验证失败")
        
        print("\n密码修复完成！")
        
    except Exception as e:
        print(f"密码修复失败: {e}")
        return False
    
    finally:
        if 'connection' in locals():
            connection.close()
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("金融企业单位物资管理系统 - 密码修复")
    print("=" * 60)
    
    if fix_passwords():
        print("\n" + "=" * 60)
        print("密码修复成功！现在可以使用以下账户登录:")
        print("管理员: admin / admin123")
        print("员工: user1 / user123")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("密码修复失败！请检查数据库连接。")
        print("=" * 60)
