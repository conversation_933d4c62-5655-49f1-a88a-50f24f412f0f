# -*- coding: utf-8 -*-
"""
供应商管理控制器
"""

from flask import Blueprint, request, redirect, url_for, flash
from .base_action import BaseAction
from service.supplier_service import SupplierService

class SupplierAction(BaseAction):
    """供应商管理控制器"""
    
    def __init__(self):
        super().__init__()
        self.supplier_service = SupplierService()
        self.blueprint = Blueprint('supplier', __name__, url_prefix='/supplier')
        self._register_routes()
    
    def _register_routes(self):
        """注册路由"""
        self.blueprint.add_url_rule('/', 'list', self.list_suppliers, methods=['GET'])
        self.blueprint.add_url_rule('/create', 'create', self.create_supplier, methods=['GET', 'POST'])
        self.blueprint.add_url_rule('/<int:supplier_id>', 'detail', self.supplier_detail, methods=['GET'])
        self.blueprint.add_url_rule('/<int:supplier_id>/edit', 'edit', self.edit_supplier, methods=['GET', 'POST'])
        self.blueprint.add_url_rule('/<int:supplier_id>/delete', 'delete', self.delete_supplier, methods=['POST'])
        self.blueprint.add_url_rule('/search', 'search', self.search_suppliers, methods=['GET'])
        self.blueprint.add_url_rule('/<int:supplier_id>/update-rating', 'update_rating', self.update_rating, methods=['POST'])
    
    def list_suppliers(self):
        """供应商列表"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            query_params = self.get_query_params()
            page = int(query_params.get('page', 1))
            per_page = int(query_params.get('per_page', 20))
            rating = query_params.get('rating')
            status = query_params.get('status')
            
            # 获取供应商列表
            if self.is_admin():
                suppliers = self.supplier_service.get_all_suppliers()
            else:
                suppliers = self.supplier_service.get_active_suppliers()
            
            # 过滤
            if rating:
                suppliers = [s for s in suppliers if s.rating == rating]
            if status:
                suppliers = [s for s in suppliers if s.status == status]
            
            # 分页
            paginated_data = self.paginate_data([s.to_dict() for s in suppliers], page, per_page)
            
            if request.is_json:
                return paginated_data
            else:
                return self.render_page('supplier/list.html', 
                                      suppliers=paginated_data['data'],
                                      pagination=paginated_data['pagination'],
                                      current_rating=rating,
                                      current_status=status)
                                      
        except Exception as e:
            return self.handle_exception(e, "获取供应商列表")
    
    def create_supplier(self):
        """创建供应商（仅管理员）"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('supplier.list'))
        
        if request.method == 'GET':
            return self.render_page('supplier/create.html')
        
        try:
            data = self.get_request_data()
            
            # 调用供应商服务创建供应商
            result = self.supplier_service.create_supplier(data, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "供应商创建成功",
                url_for('supplier.list') if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "创建供应商")
    
    def supplier_detail(self, supplier_id):
        """供应商详情"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            supplier = self.supplier_service.get_supplier_by_id(supplier_id)
            
            if not supplier:
                if request.is_json:
                    return self.error_response("供应商不存在", 404)
                else:
                    flash("供应商不存在", 'error')
                    return redirect(url_for('supplier.list'))
            
            if request.is_json:
                return self.success_response("获取成功", supplier.to_dict())
            else:
                return self.render_page('supplier/detail.html', supplier=supplier)
                
        except Exception as e:
            return self.handle_exception(e, "获取供应商详情")
    
    def edit_supplier(self, supplier_id):
        """编辑供应商（仅管理员）"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('supplier.list'))
        
        if request.method == 'GET':
            supplier = self.supplier_service.get_supplier_by_id(supplier_id)
            if not supplier:
                flash("供应商不存在", 'error')
                return redirect(url_for('supplier.list'))
            
            return self.render_page('supplier/edit.html', supplier=supplier)
        
        try:
            data = self.get_request_data()
            
            # 调用供应商服务更新供应商
            result = self.supplier_service.update_supplier(supplier_id, data, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "供应商更新成功",
                url_for('supplier.detail', supplier_id=supplier_id) if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "更新供应商")
    
    def delete_supplier(self, supplier_id):
        """删除供应商（仅管理员）"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('supplier.list'))
        
        try:
            # 调用供应商服务删除供应商
            result = self.supplier_service.delete_supplier(supplier_id, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "供应商删除成功",
                url_for('supplier.list') if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "删除供应商")
    
    def search_suppliers(self):
        """搜索供应商"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            query_params = self.get_query_params()
            keyword = query_params.get('keyword', '').strip()
            
            if not keyword:
                if request.is_json:
                    return self.success_response("搜索成功", [])
                else:
                    return redirect(url_for('supplier.list'))
            
            # 调用供应商服务搜索
            suppliers = self.supplier_service.search_suppliers(keyword)
            
            if request.is_json:
                return self.success_response("搜索成功", [s.to_dict() for s in suppliers])
            else:
                return self.render_page('supplier/list.html', 
                                      suppliers=[s.to_dict() for s in suppliers],
                                      search_keyword=keyword)
                                      
        except Exception as e:
            return self.handle_exception(e, "搜索供应商")
    
    def update_rating(self, supplier_id):
        """更新供应商评级（仅管理员）"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('supplier.list'))
        
        try:
            data = self.get_request_data()
            
            # 验证必需参数
            error = self.validate_required_params(data, ['rating'])
            if error:
                return self.error_response(error)
            
            # 调用供应商服务更新评级
            result = self.supplier_service.update_supplier_rating(
                supplier_id,
                data['rating'],
                self.get_current_user_id()
            )
            
            return self.handle_service_response(result)
            
        except Exception as e:
            return self.handle_exception(e, "更新供应商评级")
