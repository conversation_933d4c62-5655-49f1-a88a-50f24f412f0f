<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}金融企业单位物资管理系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 0.75rem 1rem;
        }
        .sidebar .nav-link:hover {
            color: #fff;
            background-color: #495057;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #007bff;
        }
        .main-content {
            margin-left: 0;
        }
        @media (min-width: 768px) {
            .main-content {
                margin-left: 250px;
            }
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card-stats {
            border-left: 4px solid #007bff;
        }
        .card-stats.success {
            border-left-color: #28a745;
        }
        .card-stats.warning {
            border-left-color: #ffc107;
        }
        .card-stats.danger {
            border-left-color: #dc3545;
        }
        .table-responsive {
            border-radius: 0.375rem;
        }
        .btn-group-sm > .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if session.user_id %}
    <!-- 侧边栏 -->
    <nav class="sidebar position-fixed top-0 start-0 d-none d-md-block" style="width: 250px; z-index: 1000;">
        <div class="p-3">
            <h5 class="text-white mb-3">
                <i class="fas fa-boxes me-2"></i>
                物资管理系统
            </h5>
            <hr class="text-white">
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('main.dashboard') }}">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        控制台
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('materials.material_list') }}">
                        <i class="fas fa-box me-2"></i>
                        物资管理
                    </a>
                </li>
                
                {% if session.role == 'admin' %}
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('materials.add_material') }}">
                        <i class="fas fa-plus me-2"></i>
                        添加物资
                    </a>
                </li>
                {% endif %}
                
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('materials.allocation_list') }}">
                        <i class="fas fa-share-alt me-2"></i>
                        分配记录
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('materials.scrap_list') }}">
                        <i class="fas fa-trash me-2"></i>
                        报废管理
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('materials.consumable_usage') }}">
                        <i class="fas fa-clipboard-list me-2"></i>
                        耗材使用
                    </a>
                </li>
                
                <hr class="text-white">
                
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('statistics.overview') }}">
                        <i class="fas fa-chart-bar me-2"></i>
                        统计概览
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('statistics.material_statistics') }}">
                        <i class="fas fa-chart-pie me-2"></i>
                        物资统计
                    </a>
                </li>
                
                {% if session.role == 'admin' %}
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('statistics.department_statistics') }}">
                        <i class="fas fa-building me-2"></i>
                        科室统计
                    </a>
                </li>
                {% endif %}
                
                <hr class="text-white">
                
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('reports.report_list') }}">
                        <i class="fas fa-file-alt me-2"></i>
                        报表中心
                    </a>
                </li>
                
                {% if session.role == 'admin' %}
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('materials.supplier_list') }}">
                        <i class="fas fa-truck me-2"></i>
                        供应商管理
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('auth.user_list') }}">
                        <i class="fas fa-users me-2"></i>
                        用户管理
                    </a>
                </li>
                {% endif %}
            </ul>
        </div>
    </nav>
    {% endif %}

    <!-- 主内容区域 -->
    <div class="main-content">
        {% if session.user_id %}
        <!-- 顶部导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
            <div class="container-fluid">
                <button class="navbar-toggler d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#sidebar">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            {{ session.real_name }}
                            {% if session.role == 'admin' %}
                            <span class="badge bg-primary ms-1">管理员</span>
                            {% endif %}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                <i class="fas fa-user-cog me-2"></i>个人信息
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>退出登录
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>
        {% endif %}

        <!-- 页面内容 -->
        <div class="container-fluid p-4">
            <!-- Flash消息 -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
