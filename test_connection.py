#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库连接和系统功能
"""

import sys
import traceback
from config import Config

def test_database_connection():
    """测试数据库连接"""
    print("=" * 50)
    print("测试数据库连接...")
    print("=" * 50)
    
    try:
        # 测试旧的数据库连接
        from database import Database
        db = Database()
        conn = db.get_connection()
        print("✓ 旧数据库连接成功!")
        conn.close()
        
        # 测试新的数据库连接
        from dao.database_connection import DatabaseConnection
        db_config = {
            'host': Config.DB_HOST,
            'user': Config.DB_USER,
            'password': Config.DB_PASSWORD,
            'database': Config.DB_NAME,
            'charset': Config.DB_CHARSET
        }
        db_conn = DatabaseConnection(db_config)
        conn = db_conn.get_connection()
        print("✓ 新数据库连接成功!")
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        traceback.print_exc()
        return False

def test_models():
    """测试模型层"""
    print("\n" + "=" * 50)
    print("测试模型层...")
    print("=" * 50)
    
    try:
        from models import User, Department, Material
        
        # 测试用户模型
        user_data = {
            'username': 'test_user',
            'email': '<EMAIL>',
            'role': 'employee',
            'department_id': 1
        }
        user = User.from_dict(user_data)
        print(f"✓ 用户模型创建成功: {user.username}")
        
        # 测试部门模型
        dept_data = {
            'name': '测试部门',
            'description': '这是一个测试部门',
            'manager_id': 1
        }
        dept = Department.from_dict(dept_data)
        print(f"✓ 部门模型创建成功: {dept.name}")
        
        # 测试物资模型
        material_data = {
            'name': '测试物资',
            'code': 'TEST001',
            'category': 'fixed_asset',
            'unit': '台',
            'price': 1000.00,
            'quantity': 10,
            'supplier_id': 1
        }
        material = Material.from_dict(material_data)
        print(f"✓ 物资模型创建成功: {material.name}")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型层测试失败: {e}")
        traceback.print_exc()
        return False

def test_dao_layer():
    """测试DAO层"""
    print("\n" + "=" * 50)
    print("测试DAO层...")
    print("=" * 50)
    
    try:
        from dao import UserDAO, DepartmentDAO, MaterialDAO
        from config import Config
        
        # 创建数据库配置
        db_config = {
            'host': Config.DB_HOST,
            'user': Config.DB_USER,
            'password': Config.DB_PASSWORD,
            'database': Config.DB_NAME,
            'charset': Config.DB_CHARSET
        }
        
        # 测试用户DAO
        user_dao = UserDAO()
        users = user_dao.find_all()
        print(f"✓ 用户DAO测试成功，找到 {len(users)} 个用户")

        # 测试部门DAO
        dept_dao = DepartmentDAO()
        departments = dept_dao.find_all()
        print(f"✓ 部门DAO测试成功，找到 {len(departments)} 个部门")

        # 测试物资DAO
        material_dao = MaterialDAO()
        materials = material_dao.find_all()
        print(f"✓ 物资DAO测试成功，找到 {len(materials)} 个物资")
        
        return True
        
    except Exception as e:
        print(f"✗ DAO层测试失败: {e}")
        traceback.print_exc()
        return False

def test_service_layer():
    """测试Service层"""
    print("\n" + "=" * 50)
    print("测试Service层...")
    print("=" * 50)
    
    try:
        from service import UserService, DepartmentService, MaterialService
        
        # 测试用户服务
        user_service = UserService()
        users = user_service.get_all_users()
        print(f"✓ 用户服务测试成功，找到 {len(users)} 个用户")
        
        # 测试部门服务
        dept_service = DepartmentService()
        departments = dept_service.get_all_departments()
        print(f"✓ 部门服务测试成功，找到 {len(departments)} 个部门")
        
        # 测试物资服务
        material_service = MaterialService()
        materials = material_service.get_all_materials()
        print(f"✓ 物资服务测试成功，找到 {len(materials)} 个物资")
        
        return True
        
    except Exception as e:
        print(f"✗ Service层测试失败: {e}")
        traceback.print_exc()
        return False

def test_action_layer():
    """测试Action层"""
    print("\n" + "=" * 50)
    print("测试Action层...")
    print("=" * 50)
    
    try:
        from action import (
            MainAction, AuthAction, MaterialAction, UserAction,
            DepartmentAction, SupplierAction, AllocationAction,
            ScrapAction, UsageAction, ReportAction
        )
        
        # 测试控制器创建
        main_action = MainAction()
        print(f"✓ 主控制器创建成功: {main_action.blueprint.name}")
        
        auth_action = AuthAction()
        print(f"✓ 认证控制器创建成功: {auth_action.blueprint.name}")
        
        material_action = MaterialAction()
        print(f"✓ 物资控制器创建成功: {material_action.blueprint.name}")
        
        user_action = UserAction()
        print(f"✓ 用户控制器创建成功: {user_action.blueprint.name}")
        
        dept_action = DepartmentAction()
        print(f"✓ 部门控制器创建成功: {dept_action.blueprint.name}")
        
        supplier_action = SupplierAction()
        print(f"✓ 供应商控制器创建成功: {supplier_action.blueprint.name}")
        
        allocation_action = AllocationAction()
        print(f"✓ 分配控制器创建成功: {allocation_action.blueprint.name}")
        
        scrap_action = ScrapAction()
        print(f"✓ 报废控制器创建成功: {scrap_action.blueprint.name}")
        
        usage_action = UsageAction()
        print(f"✓ 使用控制器创建成功: {usage_action.blueprint.name}")
        
        report_action = ReportAction()
        print(f"✓ 报表控制器创建成功: {report_action.blueprint.name}")
        
        return True
        
    except Exception as e:
        print(f"✗ Action层测试失败: {e}")
        traceback.print_exc()
        return False

def test_flask_app():
    """测试Flask应用"""
    print("\n" + "=" * 50)
    print("测试Flask应用...")
    print("=" * 50)
    
    try:
        from app import app
        
        # 测试应用创建
        print(f"✓ Flask应用创建成功")
        
        # 测试蓝图注册
        blueprints = [bp.name for bp in app.blueprints.values()]
        print(f"✓ 已注册的蓝图: {', '.join(blueprints)}")
        
        # 测试路由
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append(f"{rule.rule} [{', '.join(rule.methods)}]")
        
        print(f"✓ 已注册 {len(routes)} 个路由")
        
        return True
        
    except Exception as e:
        print(f"✗ Flask应用测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("金融企业单位物资管理系统 - 连接与功能测试")
    print("=" * 60)
    
    tests = [
        ("数据库连接", test_database_connection),
        ("模型层", test_models),
        ("DAO层", test_dao_layer),
        ("Service层", test_service_layer),
        ("Action层", test_action_layer),
        ("Flask应用", test_flask_app)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试出现异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果汇总
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关组件。")
        return 1

if __name__ == '__main__':
    sys.exit(main())
