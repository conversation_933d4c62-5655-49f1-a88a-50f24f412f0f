# -*- coding: utf-8 -*-
"""
用户管理控制器
"""

from flask import Blueprint, request, redirect, url_for, flash
from .base_action import BaseAction
from service.user_service import UserService
from service.department_service import DepartmentService

class UserAction(BaseAction):
    """用户管理控制器"""
    
    def __init__(self):
        super().__init__()
        self.user_service = UserService()
        self.department_service = DepartmentService()
        self.blueprint = Blueprint('user', __name__, url_prefix='/user')
        self._register_routes()
    
    def _register_routes(self):
        """注册路由"""
        self.blueprint.add_url_rule('/', 'list', self.list_users, methods=['GET'])
        self.blueprint.add_url_rule('/profile', 'profile', self.user_profile, methods=['GET', 'POST'])
        self.blueprint.add_url_rule('/<int:user_id>', 'detail', self.user_detail, methods=['GET'])
        self.blueprint.add_url_rule('/<int:user_id>/edit', 'edit', self.edit_user, methods=['GET', 'POST'])
        self.blueprint.add_url_rule('/<int:user_id>/delete', 'delete', self.delete_user, methods=['POST'])
        self.blueprint.add_url_rule('/<int:user_id>/activate', 'activate', self.activate_user, methods=['POST'])
        self.blueprint.add_url_rule('/<int:user_id>/deactivate', 'deactivate', self.deactivate_user, methods=['POST'])
    
    def list_users(self):
        """用户列表（仅管理员）"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('main.index'))
        
        try:
            query_params = self.get_query_params()
            page = int(query_params.get('page', 1))
            per_page = int(query_params.get('per_page', 20))
            role = query_params.get('role')
            status = query_params.get('status')
            
            # 获取用户列表
            users = self.user_service.get_all_users()
            
            # 过滤
            if role:
                users = [u for u in users if u.role == role]
            if status:
                active = status == 'active'
                users = [u for u in users if u.is_active() == active]
            
            # 分页
            paginated_data = self.paginate_data([u.to_dict() for u in users], page, per_page)
            
            if request.is_json:
                return paginated_data
            else:
                return self.render_page('user/list.html', 
                                      users=paginated_data['data'],
                                      pagination=paginated_data['pagination'],
                                      current_role=role,
                                      current_status=status)
                                      
        except Exception as e:
            return self.handle_exception(e, "获取用户列表")
    
    def user_profile(self):
        """用户个人资料"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        if request.method == 'GET':
            try:
                user = self.user_service.get_user_by_id(self.get_current_user_id())
                if not user:
                    flash('用户不存在', 'error')
                    return redirect(url_for('main.index'))
                
                return self.render_page('user/profile.html', user=user)
            except Exception as e:
                return self.handle_exception(e, "获取用户资料")
        
        # POST - 更新个人资料
        try:
            data = self.get_request_data()
            
            # 调用用户服务更新资料
            result = self.user_service.update_user_profile(self.get_current_user_id(), data)
            
            return self.handle_service_response(
                result,
                "个人资料更新成功",
                url_for('user.profile') if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "更新个人资料")
    
    def user_detail(self, user_id):
        """用户详情"""
        # 检查权限：管理员或查看自己的信息
        if not self.is_admin() and self.get_current_user_id() != user_id:
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('main.index'))
        
        try:
            user = self.user_service.get_user_by_id(user_id)
            
            if not user:
                if request.is_json:
                    return self.error_response("用户不存在", 404)
                else:
                    flash("用户不存在", 'error')
                    return redirect(url_for('user.list') if self.is_admin() else url_for('main.index'))
            
            if request.is_json:
                return self.success_response("获取成功", user.to_dict())
            else:
                return self.render_page('user/detail.html', user=user)
                
        except Exception as e:
            return self.handle_exception(e, "获取用户详情")
    
    def edit_user(self, user_id):
        """编辑用户（仅管理员）"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('main.index'))
        
        if request.method == 'GET':
            user = self.user_service.get_user_by_id(user_id)
            if not user:
                flash("用户不存在", 'error')
                return redirect(url_for('user.list'))
            
            departments = self.department_service.get_active_departments()
            return self.render_page('user/edit.html', user=user, departments=departments)
        
        try:
            data = self.get_request_data()
            
            # 调用用户服务更新用户
            result = self.user_service.update_user(user_id, data, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "用户更新成功",
                url_for('user.detail', user_id=user_id) if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "更新用户")
    
    def delete_user(self, user_id):
        """删除用户（仅管理员）"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('main.index'))
        
        try:
            # 不能删除自己
            if user_id == self.get_current_user_id():
                if request.is_json:
                    return self.error_response("不能删除自己的账户")
                else:
                    flash("不能删除自己的账户", 'error')
                    return redirect(url_for('user.list'))
            
            # 调用用户服务删除用户
            result = self.user_service.delete_user(user_id, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "用户删除成功",
                url_for('user.list') if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "删除用户")
    
    def activate_user(self, user_id):
        """激活用户（仅管理员）"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('main.index'))
        
        try:
            # 调用用户服务激活用户
            result = self.user_service.activate_user(user_id, self.get_current_user_id())
            
            return self.handle_service_response(result)
            
        except Exception as e:
            return self.handle_exception(e, "激活用户")
    
    def deactivate_user(self, user_id):
        """停用用户（仅管理员）"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('main.index'))
        
        try:
            # 不能停用自己
            if user_id == self.get_current_user_id():
                if request.is_json:
                    return self.error_response("不能停用自己的账户")
                else:
                    flash("不能停用自己的账户", 'error')
                    return redirect(url_for('user.list'))
            
            # 调用用户服务停用用户
            result = self.user_service.deactivate_user(user_id, self.get_current_user_id())
            
            return self.handle_service_response(result)
            
        except Exception as e:
            return self.handle_exception(e, "停用用户")
