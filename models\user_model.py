# -*- coding: utf-8 -*-
"""
用户模型
"""

from datetime import datetime
from typing import Optional
from .base_model import BaseModel

class User(BaseModel):
    """用户模型类"""
    
    def __init__(self, **kwargs):
        # 基本属性
        self.id: Optional[int] = kwargs.get('id')
        self.username: str = kwargs.get('username', '')
        self.password: str = kwargs.get('password', '')
        self.real_name: str = kwargs.get('real_name', '')
        self.role: str = kwargs.get('role', 'employee')  # admin, employee
        self.department_id: Optional[int] = kwargs.get('department_id')
        self.department_name: Optional[str] = kwargs.get('department_name')
        self.phone: Optional[str] = kwargs.get('phone')
        self.email: Optional[str] = kwargs.get('email')
        self.status: str = kwargs.get('status', 'active')  # active, inactive
        self.created_at: Optional[datetime] = kwargs.get('created_at')
        self.updated_at: Optional[datetime] = kwargs.get('updated_at')
        
        super().__init__(**kwargs)
    
    def is_admin(self) -> bool:
        """判断是否为管理员"""
        return self.role == 'admin'
    
    def is_active(self) -> bool:
        """判断用户是否激活"""
        return self.status == 'active'
    
    def validate(self) -> bool:
        """验证用户数据"""
        if not self.username or len(self.username) < 3:
            return False
        if not self.real_name:
            return False
        if self.role not in ['admin', 'employee']:
            return False
        if self.status not in ['active', 'inactive']:
            return False
        return True
    
    def to_dict(self, include_password=False):
        """转换为字典，默认不包含密码"""
        data = super().to_dict()
        if not include_password and 'password' in data:
            del data['password']
        return data
    
    def get_display_name(self) -> str:
        """获取显示名称"""
        return self.real_name or self.username
    
    def get_role_display(self) -> str:
        """获取角色显示名称"""
        role_map = {
            'admin': '管理员',
            'employee': '员工'
        }
        return role_map.get(self.role, self.role)
