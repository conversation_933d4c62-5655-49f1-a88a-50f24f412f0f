# -*- coding: utf-8 -*-
"""
报表服务
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
import pandas as pd
from io import BytesIO
from .base_service import BaseService
from dao.material_dao import MaterialDAO
from dao.allocation_dao import AllocationDAO
from dao.scrap_dao import ScrapDAO
from dao.usage_dao import UsageDAO
from dao.department_dao import DepartmentDAO

class ReportService(BaseService):
    """报表业务逻辑服务"""
    
    def __init__(self):
        super().__init__()
        self.material_dao = MaterialDAO()
        self.allocation_dao = AllocationDAO()
        self.scrap_dao = ScrapDAO()
        self.usage_dao = UsageDAO()
        self.department_dao = DepartmentDAO()
    
    def get_material_inventory_report(self) -> Dict[str, Any]:
        """获取物资库存报表"""
        try:
            materials = self.material_dao.find_all_with_supplier()
            
            # 按类别分组统计
            fixed_assets = [m for m in materials if m.category == 'fixed_asset']
            consumables = [m for m in materials if m.category == 'consumable']
            
            # 库存统计
            total_value = sum(float(m.price) * m.quantity for m in materials)
            fixed_asset_value = sum(float(m.price) * m.quantity for m in fixed_assets)
            consumable_value = sum(float(m.price) * m.quantity for m in consumables)
            
            # 低库存物资
            low_stock = self.material_dao.get_low_stock_materials()
            
            return {
                'summary': {
                    'total_materials': len(materials),
                    'fixed_assets_count': len(fixed_assets),
                    'consumables_count': len(consumables),
                    'total_value': total_value,
                    'fixed_asset_value': fixed_asset_value,
                    'consumable_value': consumable_value,
                    'low_stock_count': len(low_stock)
                },
                'materials': [m.to_dict() for m in materials],
                'low_stock_materials': [m.to_dict() for m in low_stock],
                'category_statistics': self.material_dao.get_statistics_by_category()
            }
        except Exception as e:
            self.logger.error(f"生成库存报表失败: {e}")
            return {'summary': {}, 'materials': [], 'low_stock_materials': [], 'category_statistics': []}
    
    def get_allocation_report(self, start_date: date = None, end_date: date = None) -> Dict[str, Any]:
        """获取物资分配报表"""
        try:
            # 如果没有指定日期，默认查询最近30天
            if not start_date:
                start_date = date.today() - timedelta(days=30)
            if not end_date:
                end_date = date.today()
            
            allocations = self.allocation_dao.find_all_with_details()
            
            # 按日期过滤
            filtered_allocations = [
                a for a in allocations 
                if start_date <= a.application_date.date() <= end_date
            ]
            
            # 按状态统计
            status_stats = {}
            for allocation in filtered_allocations:
                status = allocation.status
                if status not in status_stats:
                    status_stats[status] = {'count': 0, 'total_quantity': 0}
                status_stats[status]['count'] += 1
                status_stats[status]['total_quantity'] += allocation.quantity
            
            # 按部门统计
            dept_stats = {}
            for allocation in filtered_allocations:
                dept_name = getattr(allocation, 'department_name', '未知部门')
                if dept_name not in dept_stats:
                    dept_stats[dept_name] = {'count': 0, 'total_quantity': 0}
                dept_stats[dept_name]['count'] += 1
                dept_stats[dept_name]['total_quantity'] += allocation.quantity
            
            return {
                'summary': {
                    'total_allocations': len(filtered_allocations),
                    'date_range': f"{start_date} 至 {end_date}",
                    'status_statistics': status_stats,
                    'department_statistics': dept_stats
                },
                'allocations': [a.to_dict() for a in filtered_allocations]
            }
        except Exception as e:
            self.logger.error(f"生成分配报表失败: {e}")
            return {'summary': {}, 'allocations': []}
    
    def get_scrap_report(self, start_date: date = None, end_date: date = None) -> Dict[str, Any]:
        """获取物资报废报表"""
        try:
            # 如果没有指定日期，默认查询最近30天
            if not start_date:
                start_date = date.today() - timedelta(days=30)
            if not end_date:
                end_date = date.today()
            
            scraps = self.scrap_dao.find_all_with_details()
            
            # 按日期过滤
            filtered_scraps = [
                s for s in scraps 
                if start_date <= s.application_date.date() <= end_date
            ]
            
            # 按状态统计
            status_stats = {}
            total_estimated_value = 0
            for scrap in filtered_scraps:
                status = scrap.status
                if status not in status_stats:
                    status_stats[status] = {'count': 0, 'total_quantity': 0, 'total_value': 0}
                status_stats[status]['count'] += 1
                status_stats[status]['total_quantity'] += scrap.quantity
                if scrap.estimated_value:
                    status_stats[status]['total_value'] += float(scrap.estimated_value)
                    total_estimated_value += float(scrap.estimated_value)
            
            # 按报废类型统计
            type_stats = {}
            for scrap in filtered_scraps:
                scrap_type = scrap.scrap_type
                if scrap_type not in type_stats:
                    type_stats[scrap_type] = {'count': 0, 'total_quantity': 0}
                type_stats[scrap_type]['count'] += 1
                type_stats[scrap_type]['total_quantity'] += scrap.quantity
            
            return {
                'summary': {
                    'total_scraps': len(filtered_scraps),
                    'date_range': f"{start_date} 至 {end_date}",
                    'total_estimated_value': total_estimated_value,
                    'status_statistics': status_stats,
                    'type_statistics': type_stats
                },
                'scraps': [s.to_dict() for s in filtered_scraps]
            }
        except Exception as e:
            self.logger.error(f"生成报废报表失败: {e}")
            return {'summary': {}, 'scraps': []}
    
    def get_usage_report(self, start_date: date = None, end_date: date = None) -> Dict[str, Any]:
        """获取消耗品使用报表"""
        try:
            # 如果没有指定日期，默认查询最近30天
            if not start_date:
                start_date = date.today() - timedelta(days=30)
            if not end_date:
                end_date = date.today()
            
            # 获取使用记录
            usages = self.usage_dao.find_by_date_range(start_date, end_date)
            
            # 按部门统计
            dept_stats = self.usage_dao.get_usage_statistics_by_department(start_date, end_date)
            
            # 按物资统计
            material_stats = self.usage_dao.get_usage_statistics_by_material(start_date, end_date)
            
            # 总体统计
            total_usage_count = len(usages)
            total_quantity = sum(u.quantity for u in usages)
            
            return {
                'summary': {
                    'total_usage_count': total_usage_count,
                    'total_quantity': total_quantity,
                    'date_range': f"{start_date} 至 {end_date}",
                    'department_count': len(dept_stats),
                    'material_count': len(material_stats)
                },
                'usages': [u.to_dict() for u in usages],
                'department_statistics': dept_stats,
                'material_statistics': material_stats
            }
        except Exception as e:
            self.logger.error(f"生成使用报表失败: {e}")
            return {'summary': {}, 'usages': [], 'department_statistics': [], 'material_statistics': []}
    
    def get_comprehensive_report(self, start_date: date = None, end_date: date = None) -> Dict[str, Any]:
        """获取综合报表"""
        try:
            # 如果没有指定日期，默认查询最近30天
            if not start_date:
                start_date = date.today() - timedelta(days=30)
            if not end_date:
                end_date = date.today()
            
            # 获取各类报表数据
            inventory_report = self.get_material_inventory_report()
            allocation_report = self.get_allocation_report(start_date, end_date)
            scrap_report = self.get_scrap_report(start_date, end_date)
            usage_report = self.get_usage_report(start_date, end_date)
            
            return {
                'date_range': f"{start_date} 至 {end_date}",
                'inventory': inventory_report,
                'allocations': allocation_report,
                'scraps': scrap_report,
                'usage': usage_report,
                'generated_at': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"生成综合报表失败: {e}")
            return {}
    
    def export_report_to_excel(self, report_type: str, start_date: date = None, end_date: date = None) -> Optional[BytesIO]:
        """导出报表到Excel"""
        try:
            # 获取报表数据
            if report_type == 'inventory':
                data = self.get_material_inventory_report()
                df = pd.DataFrame(data['materials'])
            elif report_type == 'allocation':
                data = self.get_allocation_report(start_date, end_date)
                df = pd.DataFrame(data['allocations'])
            elif report_type == 'scrap':
                data = self.get_scrap_report(start_date, end_date)
                df = pd.DataFrame(data['scraps'])
            elif report_type == 'usage':
                data = self.get_usage_report(start_date, end_date)
                df = pd.DataFrame(data['usages'])
            else:
                return None
            
            # 创建Excel文件
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='数据', index=False)
                
                # 如果是综合报表，添加统计信息
                if report_type in ['allocation', 'scrap', 'usage'] and 'summary' in data:
                    summary_df = pd.DataFrame([data['summary']])
                    summary_df.to_excel(writer, sheet_name='统计', index=False)
            
            output.seek(0)
            return output
            
        except Exception as e:
            self.logger.error(f"导出Excel报表失败: {e}")
            return None
