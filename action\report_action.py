# -*- coding: utf-8 -*-
"""
报表控制器
"""

from flask import Blueprint, request, redirect, url_for, flash, send_file
from .base_action import BaseAction
from service.report_service import ReportService

class ReportAction(BaseAction):
    """报表控制器"""
    
    def __init__(self):
        super().__init__()
        self.report_service = ReportService()
        self.blueprint = Blueprint('report', __name__, url_prefix='/report')
        self._register_routes()
    
    def _register_routes(self):
        """注册路由"""
        self.blueprint.add_url_rule('/', 'index', self.report_index, methods=['GET'])
        self.blueprint.add_url_rule('/inventory', 'inventory', self.inventory_report, methods=['GET'])
        self.blueprint.add_url_rule('/allocation', 'allocation', self.allocation_report, methods=['GET'])
        self.blueprint.add_url_rule('/scrap', 'scrap', self.scrap_report, methods=['GET'])
        self.blueprint.add_url_rule('/usage', 'usage', self.usage_report, methods=['GET'])
        self.blueprint.add_url_rule('/comprehensive', 'comprehensive', self.comprehensive_report, methods=['GET'])
        self.blueprint.add_url_rule('/export/<report_type>', 'export', self.export_report, methods=['GET'])
    
    def report_index(self):
        """报表首页"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            return self.render_page('report/index.html')
        except Exception as e:
            return self.handle_exception(e, "加载报表首页")
    
    def inventory_report(self):
        """库存报表"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            # 获取库存报表数据
            report_data = self.report_service.get_material_inventory_report()
            
            if request.is_json:
                return self.success_response("获取成功", report_data)
            else:
                return self.render_page('report/inventory.html', report_data=report_data)
                
        except Exception as e:
            return self.handle_exception(e, "生成库存报表")
    
    def allocation_report(self):
        """分配报表"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            query_params = self.get_query_params()
            start_date, end_date = self.parse_date_range(
                query_params.get('start_date'),
                query_params.get('end_date')
            )
            
            # 获取分配报表数据
            report_data = self.report_service.get_allocation_report(start_date, end_date)
            
            if request.is_json:
                return self.success_response("获取成功", report_data)
            else:
                return self.render_page('report/allocation.html', 
                                      report_data=report_data,
                                      start_date=start_date,
                                      end_date=end_date)
                
        except Exception as e:
            return self.handle_exception(e, "生成分配报表")
    
    def scrap_report(self):
        """报废报表"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            query_params = self.get_query_params()
            start_date, end_date = self.parse_date_range(
                query_params.get('start_date'),
                query_params.get('end_date')
            )
            
            # 获取报废报表数据
            report_data = self.report_service.get_scrap_report(start_date, end_date)
            
            if request.is_json:
                return self.success_response("获取成功", report_data)
            else:
                return self.render_page('report/scrap.html', 
                                      report_data=report_data,
                                      start_date=start_date,
                                      end_date=end_date)
                
        except Exception as e:
            return self.handle_exception(e, "生成报废报表")
    
    def usage_report(self):
        """使用报表"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            query_params = self.get_query_params()
            start_date, end_date = self.parse_date_range(
                query_params.get('start_date'),
                query_params.get('end_date')
            )
            
            # 获取使用报表数据
            report_data = self.report_service.get_usage_report(start_date, end_date)
            
            if request.is_json:
                return self.success_response("获取成功", report_data)
            else:
                return self.render_page('report/usage.html', 
                                      report_data=report_data,
                                      start_date=start_date,
                                      end_date=end_date)
                
        except Exception as e:
            return self.handle_exception(e, "生成使用报表")
    
    def comprehensive_report(self):
        """综合报表"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('report.index'))
        
        try:
            query_params = self.get_query_params()
            start_date, end_date = self.parse_date_range(
                query_params.get('start_date'),
                query_params.get('end_date')
            )
            
            # 获取综合报表数据
            report_data = self.report_service.get_comprehensive_report(start_date, end_date)
            
            if request.is_json:
                return self.success_response("获取成功", report_data)
            else:
                return self.render_page('report/comprehensive.html', 
                                      report_data=report_data,
                                      start_date=start_date,
                                      end_date=end_date)
                
        except Exception as e:
            return self.handle_exception(e, "生成综合报表")
    
    def export_report(self, report_type):
        """导出报表"""
        # 检查登录状态
        if not self.is_logged_in():
            flash('请先登录', 'error')
            return redirect(url_for('auth.login'))
        
        # 检查报表类型权限
        if report_type == 'comprehensive' and not self.is_admin():
            flash('权限不足', 'error')
            return redirect(url_for('report.index'))
        
        try:
            query_params = self.get_query_params()
            start_date, end_date = self.parse_date_range(
                query_params.get('start_date'),
                query_params.get('end_date')
            )
            
            # 导出报表
            excel_file = self.report_service.export_report_to_excel(report_type, start_date, end_date)
            
            if excel_file:
                filename = f"{report_type}_report_{start_date or 'all'}_{end_date or 'all'}.xlsx"
                return send_file(
                    excel_file,
                    as_attachment=True,
                    download_name=filename,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
            else:
                flash('导出失败', 'error')
                return redirect(url_for('report.index'))
                
        except Exception as e:
            return self.handle_exception(e, "导出报表")
