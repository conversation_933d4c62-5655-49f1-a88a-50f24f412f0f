# -*- coding: utf-8 -*-
"""
物资DAO
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal
from .base_dao import BaseDAO
from models.material_model import Material

class MaterialDAO(BaseDAO):
    """物资数据访问对象"""
    
    def __init__(self):
        super().__init__(Material, 'materials')
    
    def insert(self, material: Material) -> Material:
        """插入新物资"""
        material.created_at = datetime.now()
        material.updated_at = datetime.now()
        
        # 如果是固定资产且没有资产编号，自动生成
        if material.is_fixed_asset() and not material.asset_number:
            material.asset_number = self._generate_asset_number()
        
        data = {
            'name': material.name,
            'category': material.category,
            'asset_number': material.asset_number,
            'specification': material.specification,
            'unit': material.unit,
            'price': float(material.price),
            'quantity': material.quantity,
            'supplier_id': material.supplier_id,
            'purchase_date': material.purchase_date,
            'warranty_period': material.warranty_period,
            'location': material.location,
            'status': material.status,
            'remark': material.remark,
            'created_at': material.created_at,
            'updated_at': material.updated_at
        }
        
        query, params = self.build_insert_query(data)
        material.id = self.db.execute_insert(query, params)
        return material
    
    def update(self, material: Material) -> Material:
        """更新物资"""
        material.updated_at = datetime.now()
        
        data = {
            'name': material.name,
            'category': material.category,
            'asset_number': material.asset_number,
            'specification': material.specification,
            'unit': material.unit,
            'price': float(material.price),
            'quantity': material.quantity,
            'supplier_id': material.supplier_id,
            'purchase_date': material.purchase_date,
            'warranty_period': material.warranty_period,
            'location': material.location,
            'status': material.status,
            'remark': material.remark,
            'updated_at': material.updated_at
        }
        
        query, params = self.build_update_query(data, material.id)
        self.db.execute_update(query, params)
        return material
    
    def find_all_with_supplier(self) -> List[Material]:
        """查找所有物资（包含供应商信息）"""
        query = """
            SELECT m.*, s.name as supplier_name 
            FROM materials m 
            LEFT JOIN suppliers s ON m.supplier_id = s.id 
            ORDER BY m.created_at DESC
        """
        results = self.db.execute_query(query, fetch_all=True)
        return [Material(**row) for row in results] if results else []
    
    def find_by_category(self, category: str) -> List[Material]:
        """根据类别查找物资"""
        query = """
            SELECT m.*, s.name as supplier_name 
            FROM materials m 
            LEFT JOIN suppliers s ON m.supplier_id = s.id 
            WHERE m.category = %s 
            ORDER BY m.name
        """
        results = self.db.execute_query(query, (category,), fetch_all=True)
        return [Material(**row) for row in results] if results else []
    
    def find_by_status(self, status: str) -> List[Material]:
        """根据状态查找物资"""
        query = """
            SELECT m.*, s.name as supplier_name 
            FROM materials m 
            LEFT JOIN suppliers s ON m.supplier_id = s.id 
            WHERE m.status = %s 
            ORDER BY m.name
        """
        results = self.db.execute_query(query, (status,), fetch_all=True)
        return [Material(**row) for row in results] if results else []
    
    def find_available_materials(self) -> List[Material]:
        """查找可用物资"""
        query = """
            SELECT m.*, s.name as supplier_name 
            FROM materials m 
            LEFT JOIN suppliers s ON m.supplier_id = s.id 
            WHERE m.status = 'in_stock' AND m.quantity > 0 
            ORDER BY m.name
        """
        results = self.db.execute_query(query, fetch_all=True)
        return [Material(**row) for row in results] if results else []
    
    def search_materials(self, keyword: str) -> List[Material]:
        """搜索物资"""
        query = """
            SELECT m.*, s.name as supplier_name 
            FROM materials m 
            LEFT JOIN suppliers s ON m.supplier_id = s.id 
            WHERE m.name LIKE %s OR m.specification LIKE %s OR m.asset_number LIKE %s
            ORDER BY m.name
        """
        search_term = f"%{keyword}%"
        results = self.db.execute_query(query, (search_term, search_term, search_term), fetch_all=True)
        return [Material(**row) for row in results] if results else []
    
    def find_by_price_range(self, min_price: Decimal, max_price: Decimal) -> List[Material]:
        """根据价格范围查找物资"""
        query = """
            SELECT m.*, s.name as supplier_name 
            FROM materials m 
            LEFT JOIN suppliers s ON m.supplier_id = s.id 
            WHERE m.price BETWEEN %s AND %s 
            ORDER BY m.price
        """
        results = self.db.execute_query(query, (float(min_price), float(max_price)), fetch_all=True)
        return [Material(**row) for row in results] if results else []
    
    def update_quantity(self, material_id: int, quantity_change: int) -> bool:
        """更新物资数量"""
        query = """
            UPDATE materials 
            SET quantity = quantity + %s, updated_at = %s 
            WHERE id = %s AND quantity + %s >= 0
        """
        result = self.db.execute_update(query, (quantity_change, datetime.now(), material_id, quantity_change))
        return result > 0
    
    def update_status(self, material_id: int, status: str) -> bool:
        """更新物资状态"""
        query = "UPDATE materials SET status = %s, updated_at = %s WHERE id = %s"
        result = self.db.execute_update(query, (status, datetime.now(), material_id))
        return result > 0
    
    def get_statistics_by_category(self) -> List[Dict[str, Any]]:
        """按类别统计物资"""
        query = """
            SELECT 
                category,
                COUNT(*) as count,
                SUM(quantity) as total_quantity,
                SUM(price * quantity) as total_value
            FROM materials 
            WHERE status != 'scrapped'
            GROUP BY category
        """
        return self.db.execute_query(query, fetch_all=True)
    
    def get_low_stock_materials(self, threshold: int = 10) -> List[Material]:
        """获取库存不足的物资"""
        query = """
            SELECT m.*, s.name as supplier_name 
            FROM materials m 
            LEFT JOIN suppliers s ON m.supplier_id = s.id 
            WHERE m.category = 'consumable' AND m.quantity <= %s AND m.status = 'in_stock'
            ORDER BY m.quantity ASC
        """
        results = self.db.execute_query(query, (threshold,), fetch_all=True)
        return [Material(**row) for row in results] if results else []
    
    def _generate_asset_number(self) -> str:
        """生成资产编号"""
        query = "SELECT MAX(CAST(SUBSTRING(asset_number, 3) AS UNSIGNED)) as max_num FROM materials WHERE asset_number LIKE 'FA%'"
        result = self.db.execute_query(query, fetch_one=True)
        
        if result and result['max_num']:
            next_num = result['max_num'] + 1
        else:
            next_num = 1
        
        return f"FA{next_num:06d}"
    
    def check_asset_number_exists(self, asset_number: str, exclude_id: Optional[int] = None) -> bool:
        """检查资产编号是否存在"""
        query = "SELECT COUNT(*) as count FROM materials WHERE asset_number = %s"
        params = [asset_number]
        
        if exclude_id:
            query += " AND id != %s"
            params.append(exclude_id)
        
        result = self.db.execute_query(query, tuple(params), fetch_one=True)
        return result['count'] > 0 if result else False
