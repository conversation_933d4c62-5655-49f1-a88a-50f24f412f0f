# -*- coding: utf-8 -*-
"""
供应商模型
"""

from datetime import datetime
from typing import Optional
from .base_model import BaseModel

class Supplier(BaseModel):
    """供应商模型类"""
    
    def __init__(self, **kwargs):
        # 基本属性
        self.id: Optional[int] = kwargs.get('id')
        self.name: str = kwargs.get('name', '')
        self.contact_person: Optional[str] = kwargs.get('contact_person')
        self.phone: Optional[str] = kwargs.get('phone')
        self.email: Optional[str] = kwargs.get('email')
        self.address: Optional[str] = kwargs.get('address')
        self.business_license: Optional[str] = kwargs.get('business_license')
        self.tax_number: Optional[str] = kwargs.get('tax_number')
        self.bank_account: Optional[str] = kwargs.get('bank_account')
        self.rating: str = kwargs.get('rating', 'B')  # A, B, C, D
        self.status: str = kwargs.get('status', 'active')  # active, inactive, blacklist
        self.remark: Optional[str] = kwargs.get('remark')
        self.created_at: Optional[datetime] = kwargs.get('created_at')
        self.updated_at: Optional[datetime] = kwargs.get('updated_at')
        
        super().__init__(**kwargs)
    
    def is_active(self) -> bool:
        """判断供应商是否激活"""
        return self.status == 'active'
    
    def is_blacklisted(self) -> bool:
        """判断是否在黑名单"""
        return self.status == 'blacklist'
    
    def validate(self) -> bool:
        """验证供应商数据"""
        if not self.name or len(self.name) < 2:
            return False
        if self.rating not in ['A', 'B', 'C', 'D']:
            return False
        if self.status not in ['active', 'inactive', 'blacklist']:
            return False
        return True
    
    def get_rating_display(self) -> str:
        """获取评级显示名称"""
        rating_map = {
            'A': '优秀',
            'B': '良好',
            'C': '一般',
            'D': '较差'
        }
        return rating_map.get(self.rating, self.rating)
    
    def get_status_display(self) -> str:
        """获取状态显示名称"""
        status_map = {
            'active': '正常',
            'inactive': '停用',
            'blacklist': '黑名单'
        }
        return status_map.get(self.status, self.status)
    
    def get_contact_info(self) -> str:
        """获取联系信息"""
        info = []
        if self.contact_person:
            info.append(f"联系人: {self.contact_person}")
        if self.phone:
            info.append(f"电话: {self.phone}")
        if self.email:
            info.append(f"邮箱: {self.email}")
        return " | ".join(info)
