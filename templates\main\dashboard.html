{% extends "base.html" %}

{% block title %}仪表板 - 物资管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">系统仪表板</h1>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                物资总数
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_materials or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                低库存物资
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ low_stock_materials or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {% if session.role == 'admin' %}
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                待审批分配
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ pending_allocations or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                待审批报废
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ pending_scraps or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-trash fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                我的分配
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ my_allocations or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                我的使用记录
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ my_usages or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-history fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- 快速操作 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">快速操作</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('material.list') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-boxes mr-2"></i>物资管理
                            </a>
                        </div>
                        {% if session.role == 'admin' %}
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('material.create') }}" class="btn btn-success btn-block">
                                <i class="fas fa-plus mr-2"></i>添加物资
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('user.list') }}" class="btn btn-info btn-block">
                                <i class="fas fa-users mr-2"></i>用户管理
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('report.index') }}" class="btn btn-warning btn-block">
                                <i class="fas fa-chart-bar mr-2"></i>统计报表
                            </a>
                        </div>
                        {% else %}
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('allocation.my_allocations') }}" class="btn btn-info btn-block">
                                <i class="fas fa-clipboard-list mr-2"></i>我的分配
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('usage.my_usages') }}" class="btn btn-success btn-block">
                                <i class="fas fa-history mr-2"></i>使用记录
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('scrap.create') }}" class="btn btn-warning btn-block">
                                <i class="fas fa-trash mr-2"></i>申请报废
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近活动 -->
    {% if recent_activities %}
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">最近活动</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        {% for activity in recent_activities %}
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{{ activity.description }}</h6>
                                <small>{{ activity.time.strftime('%Y-%m-%d %H:%M') if activity.time else '' }}</small>
                            </div>
                            {% if activity.user %}
                            <small>操作人: {{ activity.user }}</small>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
// 页面加载完成后的初始化
$(document).ready(function() {
    // 可以添加一些仪表板特定的JavaScript代码
    console.log('仪表板页面已加载');
});
</script>
{% endblock %}
