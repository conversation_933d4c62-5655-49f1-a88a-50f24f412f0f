# -*- coding: utf-8 -*-
"""
物资管理控制器
"""

from flask import Blueprint, request, redirect, url_for, flash
from .base_action import BaseAction
from service.material_service import MaterialService
from service.supplier_service import SupplierService

class MaterialAction(BaseAction):
    """物资管理控制器"""
    
    def __init__(self):
        super().__init__()
        self.material_service = MaterialService()
        self.supplier_service = SupplierService()
        self.blueprint = Blueprint('material', __name__, url_prefix='/material')
        self._register_routes()
    
    def _register_routes(self):
        """注册路由"""
        self.blueprint.add_url_rule('/', 'list', self.require_login(self.list_materials), methods=['GET'])
        self.blueprint.add_url_rule('/create', 'create', self.require_admin(self.create_material), methods=['GET', 'POST'])
        self.blueprint.add_url_rule('/<int:material_id>', 'detail', self.require_login(self.material_detail), methods=['GET'])
        self.blueprint.add_url_rule('/<int:material_id>/edit', 'edit', self.require_admin(self.edit_material), methods=['GET', 'POST'])
        self.blueprint.add_url_rule('/<int:material_id>/delete', 'delete', self.require_admin(self.delete_material), methods=['POST'])
        self.blueprint.add_url_rule('/search', 'search', self.require_login(self.search_materials), methods=['GET'])
        self.blueprint.add_url_rule('/low-stock', 'low_stock', self.require_login(self.low_stock_materials), methods=['GET'])
        self.blueprint.add_url_rule('/<int:material_id>/update-quantity', 'update_quantity', self.require_admin(self.update_quantity), methods=['POST'])
    
    def list_materials(self):
        """物资列表"""
        try:
            query_params = self.get_query_params()
            page = int(query_params.get('page', 1))
            per_page = int(query_params.get('per_page', 20))
            category = query_params.get('category')
            status = query_params.get('status')
            
            # 获取物资列表
            materials = self.material_service.get_all_materials()
            
            # 过滤
            if category:
                materials = [m for m in materials if m.category == category]
            if status:
                materials = [m for m in materials if m.status == status]
            
            # 分页
            paginated_data = self.paginate_data([m.to_dict() for m in materials], page, per_page)
            
            if request.is_json:
                return paginated_data
            else:
                return self.render_page('material/list.html', 
                                      materials=paginated_data['data'],
                                      pagination=paginated_data['pagination'],
                                      current_category=category,
                                      current_status=status)
                                      
        except Exception as e:
            return self.handle_exception(e, "获取物资列表")
    
    def create_material(self):
        """创建物资"""
        if request.method == 'GET':
            suppliers = self.supplier_service.get_active_suppliers()
            return self.render_page('material/create.html', suppliers=suppliers)
        
        try:
            data = self.get_request_data()
            
            # 调用物资服务创建物资
            result = self.material_service.create_material(data, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "物资创建成功",
                url_for('material.list') if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "创建物资")
    
    def material_detail(self, material_id):
        """物资详情"""
        try:
            material = self.material_service.get_material_by_id(material_id)
            
            if not material:
                if request.is_json:
                    return self.error_response("物资不存在", 404)
                else:
                    flash("物资不存在", 'error')
                    return redirect(url_for('material.list'))
            
            if request.is_json:
                return self.success_response("获取成功", material.to_dict())
            else:
                return self.render_page('material/detail.html', material=material)
                
        except Exception as e:
            return self.handle_exception(e, "获取物资详情")
    
    def edit_material(self, material_id):
        """编辑物资"""
        if request.method == 'GET':
            material = self.material_service.get_material_by_id(material_id)
            if not material:
                flash("物资不存在", 'error')
                return redirect(url_for('material.list'))
            
            suppliers = self.supplier_service.get_active_suppliers()
            return self.render_page('material/edit.html', material=material, suppliers=suppliers)
        
        try:
            data = self.get_request_data()
            
            # 调用物资服务更新物资
            result = self.material_service.update_material(material_id, data, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "物资更新成功",
                url_for('material.detail', material_id=material_id) if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "更新物资")
    
    def delete_material(self, material_id):
        """删除物资"""
        try:
            # 调用物资服务删除物资
            result = self.material_service.delete_material(material_id, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "物资删除成功",
                url_for('material.list') if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "删除物资")
    
    def search_materials(self):
        """搜索物资"""
        try:
            query_params = self.get_query_params()
            keyword = query_params.get('keyword', '').strip()
            
            if not keyword:
                if request.is_json:
                    return self.success_response("搜索成功", [])
                else:
                    return redirect(url_for('material.list'))
            
            # 调用物资服务搜索
            materials = self.material_service.search_materials(keyword)
            
            if request.is_json:
                return self.success_response("搜索成功", [m.to_dict() for m in materials])
            else:
                return self.render_page('material/list.html', 
                                      materials=[m.to_dict() for m in materials],
                                      search_keyword=keyword)
                                      
        except Exception as e:
            return self.handle_exception(e, "搜索物资")
    
    def low_stock_materials(self):
        """低库存物资"""
        try:
            materials = self.material_service.get_low_stock_materials()
            
            if request.is_json:
                return self.success_response("获取成功", [m.to_dict() for m in materials])
            else:
                return self.render_page('material/low_stock.html', materials=materials)
                
        except Exception as e:
            return self.handle_exception(e, "获取低库存物资")
    
    def update_quantity(self, material_id):
        """更新库存数量"""
        try:
            data = self.get_request_data()
            
            # 验证必需参数
            error = self.validate_required_params(data, ['quantity_change', 'reason'])
            if error:
                return self.error_response(error)
            
            # 调用物资服务更新库存
            result = self.material_service.update_material_quantity(
                material_id,
                int(data['quantity_change']),
                data['reason'],
                self.get_current_user_id()
            )
            
            return self.handle_service_response(result)
            
        except Exception as e:
            return self.handle_exception(e, "更新库存数量")
