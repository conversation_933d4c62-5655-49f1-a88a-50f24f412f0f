# -*- coding: utf-8 -*-
"""
部门模型
"""

from datetime import datetime
from typing import Optional
from .base_model import BaseModel

class Department(BaseModel):
    """部门模型类"""
    
    def __init__(self, **kwargs):
        # 基本属性
        self.id: Optional[int] = kwargs.get('id')
        self.name: str = kwargs.get('name', '')
        self.description: Optional[str] = kwargs.get('description')
        self.manager_id: Optional[int] = kwargs.get('manager_id')
        self.manager_name: Optional[str] = kwargs.get('manager_name')
        self.status: str = kwargs.get('status', 'active')  # active, inactive
        self.created_at: Optional[datetime] = kwargs.get('created_at')
        self.updated_at: Optional[datetime] = kwargs.get('updated_at')
        
        super().__init__(**kwargs)
    
    def is_active(self) -> bool:
        """判断部门是否激活"""
        return self.status == 'active'
    
    def validate(self) -> bool:
        """验证部门数据"""
        if not self.name or len(self.name) < 2:
            return False
        if self.status not in ['active', 'inactive']:
            return False
        return True
    
    def get_display_info(self) -> str:
        """获取部门显示信息"""
        info = self.name
        if self.manager_name:
            info += f" (负责人: {self.manager_name})"
        return info
