# -*- coding: utf-8 -*-
"""
用户服务
"""

from typing import Optional, List, Dict, Any
from .base_service import BaseService
from dao.user_dao import UserDAO
from dao.department_dao import DepartmentDAO
from models.user_model import User

class UserService(BaseService):
    """用户业务逻辑服务"""
    
    def __init__(self):
        super().__init__()
        self.user_dao = UserDAO()
        self.department_dao = DepartmentDAO()
    
    def authenticate(self, username: str, password: str) -> Optional[User]:
        """用户认证"""
        try:
            if not username or not password:
                return None
            
            user = self.user_dao.authenticate(username, password)
            if user:
                self.log_operation("用户登录", user.id, f"用户名: {username}")
            
            return user
        except Exception as e:
            self.logger.error(f"用户认证失败: {e}")
            return None
    
    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        try:
            return self.user_dao.find_by_id(user_id)
        except Exception as e:
            self.logger.error(f"获取用户失败: {e}")
            return None
    
    def get_all_users(self) -> List[User]:
        """获取所有用户"""
        try:
            return self.user_dao.find_all_with_department()
        except Exception as e:
            self.logger.error(f"获取用户列表失败: {e}")
            return []
    
    def create_user(self, user_data: Dict[str, Any], operator_id: int) -> Dict[str, Any]:
        """创建用户"""
        try:
            # 验证必填字段
            required_fields = ['username', 'password', 'real_name', 'role', 'department_id']
            error = self.validate_required_fields(user_data, required_fields)
            if error:
                return self.create_error_response(error)
            
            # 验证字段格式
            error = self.validate_string_length(user_data['username'], '用户名', 3, 50)
            if error:
                return self.create_error_response(error)
            
            error = self.validate_string_length(user_data['password'], '密码', 6, 100)
            if error:
                return self.create_error_response(error)
            
            error = self.validate_string_length(user_data['real_name'], '真实姓名', 2, 50)
            if error:
                return self.create_error_response(error)
            
            error = self.validate_choice(user_data['role'], '角色', ['admin', 'employee'])
            if error:
                return self.create_error_response(error)
            
            # 检查用户名是否已存在
            if self.user_dao.check_username_exists(user_data['username']):
                return self.create_error_response("用户名已存在")
            
            # 检查部门是否存在
            department = self.department_dao.find_by_id(user_data['department_id'])
            if not department:
                return self.create_error_response("指定的部门不存在")
            
            # 创建用户对象
            user = User(**user_data)
            
            # 验证用户数据
            if not user.validate():
                return self.create_error_response("用户数据验证失败")
            
            # 保存用户
            created_user = self.user_dao.insert(user)
            
            self.log_operation("创建用户", operator_id, f"新用户: {created_user.username}")
            
            return self.create_success_response("用户创建成功", created_user.to_dict())
            
        except Exception as e:
            return self.handle_service_error(e, "创建用户")
    
    def update_user(self, user_id: int, user_data: Dict[str, Any], operator_id: int) -> Dict[str, Any]:
        """更新用户"""
        try:
            # 获取现有用户
            user = self.user_dao.find_by_id(user_id)
            if not user:
                return self.create_error_response("用户不存在")
            
            # 验证必填字段
            required_fields = ['username', 'real_name', 'role', 'department_id']
            error = self.validate_required_fields(user_data, required_fields)
            if error:
                return self.create_error_response(error)
            
            # 验证字段格式
            error = self.validate_string_length(user_data['username'], '用户名', 3, 50)
            if error:
                return self.create_error_response(error)
            
            error = self.validate_string_length(user_data['real_name'], '真实姓名', 2, 50)
            if error:
                return self.create_error_response(error)
            
            error = self.validate_choice(user_data['role'], '角色', ['admin', 'employee'])
            if error:
                return self.create_error_response(error)
            
            # 检查用户名是否已存在（排除当前用户）
            if self.user_dao.check_username_exists(user_data['username'], user_id):
                return self.create_error_response("用户名已存在")
            
            # 检查部门是否存在
            department = self.department_dao.find_by_id(user_data['department_id'])
            if not department:
                return self.create_error_response("指定的部门不存在")
            
            # 更新用户属性
            user.update(**user_data)
            
            # 验证用户数据
            if not user.validate():
                return self.create_error_response("用户数据验证失败")
            
            # 保存用户
            updated_user = self.user_dao.update(user)
            
            self.log_operation("更新用户", operator_id, f"用户: {updated_user.username}")
            
            return self.create_success_response("用户更新成功", updated_user.to_dict())
            
        except Exception as e:
            return self.handle_service_error(e, "更新用户")
    
    def delete_user(self, user_id: int, operator_id: int) -> Dict[str, Any]:
        """删除用户"""
        try:
            # 获取用户
            user = self.user_dao.find_by_id(user_id)
            if not user:
                return self.create_error_response("用户不存在")
            
            # 不能删除自己
            if user_id == operator_id:
                return self.create_error_response("不能删除自己的账户")
            
            # 软删除：更新状态为inactive
            success = self.user_dao.update_status(user_id, 'inactive')
            
            if success:
                self.log_operation("删除用户", operator_id, f"用户: {user.username}")
                return self.create_success_response("用户删除成功")
            else:
                return self.create_error_response("删除用户失败")
                
        except Exception as e:
            return self.handle_service_error(e, "删除用户")
    
    def change_password(self, user_id: int, old_password: str, new_password: str) -> Dict[str, Any]:
        """修改密码"""
        try:
            # 获取用户
            user = self.user_dao.find_by_id(user_id)
            if not user:
                return self.create_error_response("用户不存在")
            
            # 验证旧密码
            if not self.user_dao.authenticate(user.username, old_password):
                return self.create_error_response("原密码错误")
            
            # 验证新密码
            error = self.validate_string_length(new_password, '新密码', 6, 100)
            if error:
                return self.create_error_response(error)
            
            # 更新密码
            success = self.user_dao.update_password(user_id, new_password)
            
            if success:
                self.log_operation("修改密码", user_id)
                return self.create_success_response("密码修改成功")
            else:
                return self.create_error_response("密码修改失败")
                
        except Exception as e:
            return self.handle_service_error(e, "修改密码")
    
    def get_users_by_department(self, department_id: int) -> List[User]:
        """根据部门获取用户"""
        try:
            return self.user_dao.find_by_department(department_id)
        except Exception as e:
            self.logger.error(f"获取部门用户失败: {e}")
            return []
    
    def get_admin_users(self) -> List[User]:
        """获取管理员用户"""
        try:
            return self.user_dao.find_by_role('admin')
        except Exception as e:
            self.logger.error(f"获取管理员用户失败: {e}")
            return []
