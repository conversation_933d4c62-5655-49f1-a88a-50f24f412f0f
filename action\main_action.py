# -*- coding: utf-8 -*-
"""
主控制器
"""

from flask import Blueprint, request, redirect, url_for, flash, session
from .base_action import BaseAction
from service.material_service import MaterialService
from service.allocation_service import AllocationService
from service.scrap_service import ScrapService
from service.usage_service import UsageService
from service.report_service import ReportService

class MainAction(BaseAction):
    """主控制器"""
    
    def __init__(self):
        super().__init__()
        self.material_service = MaterialService()
        self.allocation_service = AllocationService()
        self.scrap_service = ScrapService()
        self.usage_service = UsageService()
        self.report_service = ReportService()
        self.blueprint = Blueprint('main', __name__)
        self._register_routes()
    
    def _register_routes(self):
        """注册路由"""
        self.blueprint.add_url_rule('/', 'index', self.index, methods=['GET'])
        self.blueprint.add_url_rule('/dashboard', 'dashboard', self.dashboard, methods=['GET'])
        self.blueprint.add_url_rule('/search', 'search', self.search, methods=['GET'])
        self.blueprint.add_url_rule('/notifications', 'notifications', self.notifications, methods=['GET'])
        self.blueprint.add_url_rule('/help', 'help', self.help, methods=['GET'])
    
    def index(self):
        """首页"""
        # 如果用户已登录，重定向到仪表板
        if self.is_logged_in():
            return redirect(url_for('main.dashboard'))
        else:
            return redirect(url_for('auth.login'))
    
    def dashboard(self):
        """仪表板"""
        # 检查登录状态
        self.logger.info(f"Dashboard访问 - 用户ID: {session.get('user_id')}, 登录状态: {self.is_logged_in()}")
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            # 获取仪表板数据
            self.logger.info("开始获取仪表板数据")
            dashboard_data = self._get_dashboard_data()
            self.logger.info(f"仪表板数据获取成功: {len(dashboard_data)} 项")

            if request.is_json:
                return self.success_response("获取成功", dashboard_data)
            else:
                self.logger.info("开始渲染仪表板模板")
                return self.render_page('main/dashboard.html', **dashboard_data)

        except Exception as e:
            self.logger.error(f"仪表板加载异常: {str(e)}")
            return self.handle_exception(e, "加载仪表板")
    
    def search(self):
        """全局搜索"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            query_params = self.get_query_params()
            keyword = query_params.get('keyword', '').strip()
            search_type = query_params.get('type', 'all')  # all, material, allocation, scrap, usage
            
            if not keyword:
                if request.is_json:
                    return self.success_response("搜索成功", {'results': []})
                else:
                    return self.render_page('main/search.html', results=[], keyword=keyword)
            
            # 执行搜索
            search_results = self._perform_search(keyword, search_type)
            
            if request.is_json:
                return self.success_response("搜索成功", {'results': search_results})
            else:
                return self.render_page('main/search.html', 
                                      results=search_results,
                                      keyword=keyword,
                                      search_type=search_type)
                                      
        except Exception as e:
            return self.handle_exception(e, "执行搜索")
    
    def notifications(self):
        """通知中心"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            # 获取通知数据
            notifications = self._get_notifications()
            
            if request.is_json:
                return self.success_response("获取成功", notifications)
            else:
                return self.render_page('main/notifications.html', notifications=notifications)
                
        except Exception as e:
            return self.handle_exception(e, "获取通知")
    
    def help(self):
        """帮助页面"""
        try:
            return self.render_page('main/help.html')
        except Exception as e:
            return self.handle_exception(e, "加载帮助页面")
    
    def _get_dashboard_data(self):
        """获取仪表板数据"""
        dashboard_data = {}

        try:
            # 基础统计数据
            self.logger.info("开始获取仪表板数据")
            dashboard_data['total_materials'] = 0  # 暂时简化
            dashboard_data['low_stock_materials'] = 0
            dashboard_data['pending_allocations'] = 0
            dashboard_data['pending_scraps'] = 0
            dashboard_data['recent_activities'] = []
            dashboard_data['recent_usages'] = []
            dashboard_data['my_allocations'] = 0
            dashboard_data['my_usages'] = 0
            dashboard_data['inventory_summary'] = {}

            self.logger.info("仪表板数据设置完成")

        except Exception as e:
            self.logger.error(f"获取仪表板数据失败: {str(e)}")
            # 返回默认数据
            dashboard_data = {
                'total_materials': 0,
                'low_stock_materials': 0,
                'pending_allocations': 0,
                'pending_scraps': 0,
                'recent_activities': [],
                'recent_usages': [],
                'my_allocations': 0,
                'my_usages': 0,
                'inventory_summary': {}
            }

        return dashboard_data
    
    def _perform_search(self, keyword, search_type):
        """执行搜索"""
        results = []
        
        try:
            if search_type in ['all', 'material']:
                # 搜索物资
                materials = self.material_service.search_materials(keyword)
                for material in materials:
                    results.append({
                        'type': 'material',
                        'title': material.name,
                        'description': f"编号: {material.code}, 类型: {material.category}",
                        'url': url_for('material.detail', material_id=material.id)
                    })
            
            # 可以添加更多搜索类型
            # if search_type in ['all', 'allocation']:
            #     # 搜索分配记录
            #     pass
            
        except Exception as e:
            self.logger.error(f"搜索失败: {str(e)}")
        
        return results
    
    def _get_notifications(self):
        """获取通知"""
        notifications = []
        
        try:
            # 低库存提醒
            low_stock_materials = self.material_service.get_low_stock_materials()
            for material in low_stock_materials:
                notifications.append({
                    'type': 'warning',
                    'title': '库存不足提醒',
                    'message': f"物资 {material.name} 库存不足，当前库存: {material.quantity}",
                    'time': material.updated_at,
                    'url': url_for('material.detail', material_id=material.id)
                })
            
            # 待审批提醒（仅管理员）
            if self.is_admin():
                pending_allocations = self.allocation_service.get_pending_allocations()
                if pending_allocations:
                    notifications.append({
                        'type': 'info',
                        'title': '待审批分配申请',
                        'message': f"有 {len(pending_allocations)} 个分配申请待审批",
                        'time': None,
                        'url': url_for('allocation.pending')
                    })
                
                pending_scraps = self.scrap_service.get_pending_scraps()
                if pending_scraps:
                    notifications.append({
                        'type': 'info',
                        'title': '待审批报废申请',
                        'message': f"有 {len(pending_scraps)} 个报废申请待审批",
                        'time': None,
                        'url': url_for('scrap.pending')
                    })
            
        except Exception as e:
            self.logger.error(f"获取通知失败: {str(e)}")
        
        return notifications
    
    def _get_recent_activities(self):
        """获取最近活动"""
        activities = []
        
        try:
            # 获取最近的使用记录
            recent_usages = self.usage_service.get_recent_usages(5)
            for usage in recent_usages:
                activities.append({
                    'type': 'usage',
                    'description': f"使用了 {usage.quantity} 个 {usage.material_name}",
                    'time': usage.created_at,
                    'user': usage.user_name
                })
            
            # 可以添加更多活动类型
            
        except Exception as e:
            self.logger.error(f"获取最近活动失败: {str(e)}")
        
        return activities
