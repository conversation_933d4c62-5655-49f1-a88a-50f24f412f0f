# -*- coding: utf-8 -*-
"""
物资服务
"""

from typing import Optional, List, Dict, Any
from decimal import Decimal
from datetime import datetime
from .base_service import BaseService
from dao.material_dao import MaterialDAO
from dao.supplier_dao import SupplierDAO
from models.material_model import Material

class MaterialService(BaseService):
    """物资业务逻辑服务"""
    
    def __init__(self):
        super().__init__()
        self.material_dao = MaterialDAO()
        self.supplier_dao = SupplierDAO()
    
    def get_all_materials(self) -> List[Material]:
        """获取所有物资"""
        try:
            return self.material_dao.find_all_with_supplier()
        except Exception as e:
            self.logger.error(f"获取物资列表失败: {e}")
            return []
    
    def get_material_by_id(self, material_id: int) -> Optional[Material]:
        """根据ID获取物资"""
        try:
            return self.material_dao.find_by_id(material_id)
        except Exception as e:
            self.logger.error(f"获取物资失败: {e}")
            return None
    
    def create_material(self, material_data: Dict[str, Any], operator_id: int) -> Dict[str, Any]:
        """创建物资"""
        try:
            # 验证必填字段
            required_fields = ['name', 'category', 'unit', 'price', 'quantity']
            error = self.validate_required_fields(material_data, required_fields)
            if error:
                return self.create_error_response(error)
            
            # 验证字段格式
            error = self.validate_string_length(material_data['name'], '物资名称', 2, 100)
            if error:
                return self.create_error_response(error)
            
            error = self.validate_choice(material_data['category'], '物资类别', ['fixed_asset', 'consumable'])
            if error:
                return self.create_error_response(error)
            
            error = self.validate_positive_number(material_data['price'], '价格')
            if error:
                return self.create_error_response(error)
            
            error = self.validate_non_negative_number(material_data['quantity'], '数量')
            if error:
                return self.create_error_response(error)
            
            # 验证供应商（如果提供）
            if material_data.get('supplier_id'):
                supplier = self.supplier_dao.find_by_id(material_data['supplier_id'])
                if not supplier:
                    return self.create_error_response("指定的供应商不存在")
                if not supplier.is_active():
                    return self.create_error_response("指定的供应商已停用")
            
            # 验证资产编号（如果是固定资产且提供了编号）
            if (material_data['category'] == 'fixed_asset' and 
                material_data.get('asset_number') and 
                self.material_dao.check_asset_number_exists(material_data['asset_number'])):
                return self.create_error_response("资产编号已存在")
            
            # 创建物资对象
            material = Material(**material_data)
            
            # 验证物资数据
            if not material.validate():
                return self.create_error_response("物资数据验证失败")
            
            # 保存物资
            created_material = self.material_dao.insert(material)
            
            self.log_operation("创建物资", operator_id, f"物资: {created_material.name}")
            
            return self.create_success_response("物资创建成功", created_material.to_dict())
            
        except Exception as e:
            return self.handle_service_error(e, "创建物资")
    
    def update_material(self, material_id: int, material_data: Dict[str, Any], operator_id: int) -> Dict[str, Any]:
        """更新物资"""
        try:
            # 获取现有物资
            material = self.material_dao.find_by_id(material_id)
            if not material:
                return self.create_error_response("物资不存在")
            
            # 验证必填字段
            required_fields = ['name', 'category', 'unit', 'price', 'quantity']
            error = self.validate_required_fields(material_data, required_fields)
            if error:
                return self.create_error_response(error)
            
            # 验证字段格式
            error = self.validate_string_length(material_data['name'], '物资名称', 2, 100)
            if error:
                return self.create_error_response(error)
            
            error = self.validate_choice(material_data['category'], '物资类别', ['fixed_asset', 'consumable'])
            if error:
                return self.create_error_response(error)
            
            error = self.validate_positive_number(material_data['price'], '价格')
            if error:
                return self.create_error_response(error)
            
            error = self.validate_non_negative_number(material_data['quantity'], '数量')
            if error:
                return self.create_error_response(error)
            
            # 验证供应商（如果提供）
            if material_data.get('supplier_id'):
                supplier = self.supplier_dao.find_by_id(material_data['supplier_id'])
                if not supplier:
                    return self.create_error_response("指定的供应商不存在")
            
            # 验证资产编号（如果是固定资产且提供了编号）
            if (material_data['category'] == 'fixed_asset' and 
                material_data.get('asset_number') and 
                self.material_dao.check_asset_number_exists(material_data['asset_number'], material_id)):
                return self.create_error_response("资产编号已存在")
            
            # 更新物资属性
            material.update(**material_data)
            
            # 验证物资数据
            if not material.validate():
                return self.create_error_response("物资数据验证失败")
            
            # 保存物资
            updated_material = self.material_dao.update(material)
            
            self.log_operation("更新物资", operator_id, f"物资: {updated_material.name}")
            
            return self.create_success_response("物资更新成功", updated_material.to_dict())
            
        except Exception as e:
            return self.handle_service_error(e, "更新物资")
    
    def delete_material(self, material_id: int, operator_id: int) -> Dict[str, Any]:
        """删除物资"""
        try:
            # 获取物资
            material = self.material_dao.find_by_id(material_id)
            if not material:
                return self.create_error_response("物资不存在")
            
            # 检查是否可以删除（没有分配记录等）
            # 这里可以添加更多的业务规则检查
            
            # 软删除：更新状态为scrapped
            success = self.material_dao.update_status(material_id, 'scrapped')
            
            if success:
                self.log_operation("删除物资", operator_id, f"物资: {material.name}")
                return self.create_success_response("物资删除成功")
            else:
                return self.create_error_response("删除物资失败")
                
        except Exception as e:
            return self.handle_service_error(e, "删除物资")
    
    def search_materials(self, keyword: str) -> List[Material]:
        """搜索物资"""
        try:
            if not keyword or len(keyword.strip()) < 2:
                return []
            return self.material_dao.search_materials(keyword.strip())
        except Exception as e:
            self.logger.error(f"搜索物资失败: {e}")
            return []
    
    def get_materials_by_category(self, category: str) -> List[Material]:
        """根据类别获取物资"""
        try:
            return self.material_dao.find_by_category(category)
        except Exception as e:
            self.logger.error(f"获取物资失败: {e}")
            return []
    
    def get_available_materials(self) -> List[Material]:
        """获取可用物资"""
        try:
            return self.material_dao.find_available_materials()
        except Exception as e:
            self.logger.error(f"获取可用物资失败: {e}")
            return []
    
    def get_low_stock_materials(self, threshold: int = 10) -> List[Material]:
        """获取库存不足的物资"""
        try:
            return self.material_dao.get_low_stock_materials(threshold)
        except Exception as e:
            self.logger.error(f"获取库存不足物资失败: {e}")
            return []
    
    def update_material_quantity(self, material_id: int, quantity_change: int, operator_id: int) -> Dict[str, Any]:
        """更新物资数量"""
        try:
            # 获取物资
            material = self.material_dao.find_by_id(material_id)
            if not material:
                return self.create_error_response("物资不存在")
            
            # 检查数量变化是否合理
            if material.quantity + quantity_change < 0:
                return self.create_error_response("库存数量不足")
            
            # 更新数量
            success = self.material_dao.update_quantity(material_id, quantity_change)
            
            if success:
                action = "增加" if quantity_change > 0 else "减少"
                self.log_operation(f"{action}物资数量", operator_id, 
                                 f"物资: {material.name}, 变化: {quantity_change}")
                return self.create_success_response("物资数量更新成功")
            else:
                return self.create_error_response("物资数量更新失败")
                
        except Exception as e:
            return self.handle_service_error(e, "更新物资数量")
    
    def get_material_statistics(self) -> Dict[str, Any]:
        """获取物资统计信息"""
        try:
            stats = self.material_dao.get_statistics_by_category()
            
            total_count = sum(item['count'] for item in stats)
            total_value = sum(item['total_value'] for item in stats)
            
            return {
                'total_count': total_count,
                'total_value': float(total_value),
                'by_category': stats,
                'low_stock_count': len(self.get_low_stock_materials())
            }
        except Exception as e:
            self.logger.error(f"获取物资统计失败: {e}")
            return {
                'total_count': 0,
                'total_value': 0,
                'by_category': [],
                'low_stock_count': 0
            }
