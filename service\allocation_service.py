# -*- coding: utf-8 -*-
"""
物资分配服务
"""

from typing import Optional, List, Dict, Any
from datetime import datetime, date
from .base_service import BaseService
from dao.allocation_dao import AllocationDAO
from dao.material_dao import MaterialDAO
from dao.department_dao import DepartmentDAO
from dao.user_dao import UserDAO
from models.allocation_model import MaterialAllocation

class AllocationService(BaseService):
    """物资分配业务逻辑服务"""
    
    def __init__(self):
        super().__init__()
        self.allocation_dao = AllocationDAO()
        self.material_dao = MaterialDAO()
        self.department_dao = DepartmentDAO()
        self.user_dao = UserDAO()
    
    def get_all_allocations(self) -> List[MaterialAllocation]:
        """获取所有分配记录"""
        try:
            return self.allocation_dao.find_all_with_details()
        except Exception as e:
            self.logger.error(f"获取分配记录失败: {e}")
            return []
    
    def get_allocation_by_id(self, allocation_id: int) -> Optional[MaterialAllocation]:
        """根据ID获取分配记录"""
        try:
            return self.allocation_dao.find_by_id(allocation_id)
        except Exception as e:
            self.logger.error(f"获取分配记录失败: {e}")
            return None
    
    def create_allocation_request(self, allocation_data: Dict[str, Any], applicant_id: int) -> Dict[str, Any]:
        """创建分配申请"""
        try:
            # 验证必填字段
            required_fields = ['material_id', 'department_id', 'quantity', 'reason']
            error = self.validate_required_fields(allocation_data, required_fields)
            if error:
                return self.create_error_response(error)
            
            # 验证数量
            error = self.validate_positive_number(allocation_data['quantity'], '申请数量')
            if error:
                return self.create_error_response(error)
            
            # 验证物资是否存在且可用
            material = self.material_dao.find_by_id(allocation_data['material_id'])
            if not material:
                return self.create_error_response("指定的物资不存在")
            
            if material.status != 'in_stock':
                return self.create_error_response("物资状态不可用")
            
            # 验证库存是否充足
            if material.quantity < allocation_data['quantity']:
                return self.create_error_response(f"库存不足，当前库存: {material.quantity}")
            
            # 验证部门是否存在
            department = self.department_dao.find_by_id(allocation_data['department_id'])
            if not department:
                return self.create_error_response("指定的部门不存在")
            
            if not department.is_active():
                return self.create_error_response("指定的部门已停用")
            
            # 创建分配申请
            allocation_data.update({
                'applicant_id': applicant_id,
                'application_date': datetime.now(),
                'status': 'pending'
            })
            
            allocation = MaterialAllocation(**allocation_data)
            
            # 验证分配数据
            if not allocation.validate():
                return self.create_error_response("分配申请数据验证失败")
            
            # 保存分配申请
            created_allocation = self.allocation_dao.insert(allocation)
            
            self.log_operation("创建分配申请", applicant_id, 
                             f"物资: {material.name}, 数量: {allocation_data['quantity']}")
            
            return self.create_success_response("分配申请创建成功", created_allocation.to_dict())
            
        except Exception as e:
            return self.handle_service_error(e, "创建分配申请")
    
    def approve_allocation(self, allocation_id: int, approver_id: int, comment: str = None) -> Dict[str, Any]:
        """审批分配申请"""
        try:
            # 获取分配申请
            allocation = self.allocation_dao.find_by_id(allocation_id)
            if not allocation:
                return self.create_error_response("分配申请不存在")
            
            if allocation.status != 'pending':
                return self.create_error_response("分配申请状态不正确，无法审批")
            
            # 再次检查库存
            material = self.material_dao.find_by_id(allocation.material_id)
            if material.quantity < allocation.quantity:
                return self.create_error_response(f"库存不足，当前库存: {material.quantity}")
            
            # 审批通过
            success = self.allocation_dao.approve_allocation(allocation_id, approver_id, comment)
            
            if success:
                # 减少库存
                self.material_dao.update_quantity(allocation.material_id, -allocation.quantity)
                
                self.log_operation("审批分配申请", approver_id, 
                                 f"申请ID: {allocation_id}, 审批通过")
                
                return self.create_success_response("分配申请审批通过")
            else:
                return self.create_error_response("审批失败")
                
        except Exception as e:
            return self.handle_service_error(e, "审批分配申请")
    
    def reject_allocation(self, allocation_id: int, approver_id: int, comment: str = None) -> Dict[str, Any]:
        """拒绝分配申请"""
        try:
            # 获取分配申请
            allocation = self.allocation_dao.find_by_id(allocation_id)
            if not allocation:
                return self.create_error_response("分配申请不存在")
            
            if allocation.status != 'pending':
                return self.create_error_response("分配申请状态不正确，无法拒绝")
            
            # 拒绝申请
            success = self.allocation_dao.reject_allocation(allocation_id, approver_id, comment)
            
            if success:
                self.log_operation("拒绝分配申请", approver_id, 
                                 f"申请ID: {allocation_id}, 拒绝原因: {comment}")
                
                return self.create_success_response("分配申请已拒绝")
            else:
                return self.create_error_response("拒绝失败")
                
        except Exception as e:
            return self.handle_service_error(e, "拒绝分配申请")
    
    def receive_allocation(self, allocation_id: int, receiver_id: int) -> Dict[str, Any]:
        """接收分配的物资"""
        try:
            # 获取分配申请
            allocation = self.allocation_dao.find_by_id(allocation_id)
            if not allocation:
                return self.create_error_response("分配申请不存在")
            
            if allocation.status != 'approved':
                return self.create_error_response("分配申请状态不正确，无法接收")
            
            # 接收物资
            success = self.allocation_dao.receive_allocation(allocation_id, receiver_id)
            
            if success:
                self.log_operation("接收分配物资", receiver_id, 
                                 f"申请ID: {allocation_id}")
                
                return self.create_success_response("物资接收成功")
            else:
                return self.create_error_response("接收失败")
                
        except Exception as e:
            return self.handle_service_error(e, "接收分配物资")
    
    def get_allocations_by_status(self, status: str) -> List[MaterialAllocation]:
        """根据状态获取分配记录"""
        try:
            return self.allocation_dao.find_by_status(status)
        except Exception as e:
            self.logger.error(f"获取分配记录失败: {e}")
            return []
    
    def get_allocations_by_department(self, department_id: int) -> List[MaterialAllocation]:
        """根据部门获取分配记录"""
        try:
            return self.allocation_dao.find_by_department(department_id)
        except Exception as e:
            self.logger.error(f"获取部门分配记录失败: {e}")
            return []
    
    def get_allocations_by_applicant(self, applicant_id: int) -> List[MaterialAllocation]:
        """根据申请人获取分配记录"""
        try:
            return self.allocation_dao.find_by_applicant(applicant_id)
        except Exception as e:
            self.logger.error(f"获取申请人分配记录失败: {e}")
            return []
    
    def get_pending_allocations(self) -> List[MaterialAllocation]:
        """获取待审批的分配申请"""
        return self.get_allocations_by_status('pending')
    
    def cancel_allocation(self, allocation_id: int, operator_id: int) -> Dict[str, Any]:
        """取消分配申请"""
        try:
            # 获取分配申请
            allocation = self.allocation_dao.find_by_id(allocation_id)
            if not allocation:
                return self.create_error_response("分配申请不存在")
            
            if allocation.status not in ['pending', 'approved']:
                return self.create_error_response("分配申请状态不正确，无法取消")
            
            # 如果已审批通过，需要恢复库存
            if allocation.status == 'approved':
                self.material_dao.update_quantity(allocation.material_id, allocation.quantity)
            
            # 更新状态为取消
            allocation.status = 'cancelled'
            self.allocation_dao.update(allocation)
            
            self.log_operation("取消分配申请", operator_id, f"申请ID: {allocation_id}")
            
            return self.create_success_response("分配申请已取消")
            
        except Exception as e:
            return self.handle_service_error(e, "取消分配申请")
