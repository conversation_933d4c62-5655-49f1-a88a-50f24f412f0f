# -*- coding: utf-8 -*-
"""
物资分配模型
"""

from datetime import datetime
from typing import Optional
from .base_model import BaseModel

class MaterialAllocation(BaseModel):
    """物资分配模型类"""
    
    def __init__(self, **kwargs):
        # 基本属性
        self.id: Optional[int] = kwargs.get('id')
        self.material_id: int = kwargs.get('material_id')
        self.material_name: Optional[str] = kwargs.get('material_name')
        self.department_id: int = kwargs.get('department_id')
        self.department_name: Optional[str] = kwargs.get('department_name')
        self.quantity: int = kwargs.get('quantity', 1)
        self.applicant_id: int = kwargs.get('applicant_id')
        self.applicant_name: Optional[str] = kwargs.get('applicant_name')
        self.application_date: datetime = kwargs.get('application_date', datetime.now())
        self.reason: Optional[str] = kwargs.get('reason')
        self.status: str = kwargs.get('status', 'pending')  # pending, approved, rejected, received
        self.approver_id: Optional[int] = kwargs.get('approver_id')
        self.approver_name: Optional[str] = kwargs.get('approver_name')
        self.approval_date: Optional[datetime] = kwargs.get('approval_date')
        self.approval_comment: Optional[str] = kwargs.get('approval_comment')
        self.receiver_id: Optional[int] = kwargs.get('receiver_id')
        self.receiver_name: Optional[str] = kwargs.get('receiver_name')
        self.receive_date: Optional[datetime] = kwargs.get('receive_date')
        self.created_at: Optional[datetime] = kwargs.get('created_at')
        self.updated_at: Optional[datetime] = kwargs.get('updated_at')
        
        super().__init__(**kwargs)
    
    def is_pending(self) -> bool:
        """判断是否待审批"""
        return self.status == 'pending'
    
    def is_approved(self) -> bool:
        """判断是否已审批"""
        return self.status == 'approved'
    
    def is_rejected(self) -> bool:
        """判断是否已拒绝"""
        return self.status == 'rejected'
    
    def is_received(self) -> bool:
        """判断是否已接收"""
        return self.status == 'received'
    
    def can_approve(self) -> bool:
        """判断是否可以审批"""
        return self.status == 'pending'
    
    def can_receive(self) -> bool:
        """判断是否可以接收"""
        return self.status == 'approved'
    
    def validate(self) -> bool:
        """验证分配数据"""
        if not self.material_id or not self.department_id or not self.applicant_id:
            return False
        if self.quantity <= 0:
            return False
        if self.status not in ['pending', 'approved', 'rejected', 'received']:
            return False
        return True
    
    def get_status_display(self) -> str:
        """获取状态显示名称"""
        status_map = {
            'pending': '待审批',
            'approved': '已审批',
            'rejected': '已拒绝',
            'received': '已接收'
        }
        return status_map.get(self.status, self.status)
    
    def get_workflow_info(self) -> dict:
        """获取工作流信息"""
        return {
            'application': {
                'user': self.applicant_name,
                'date': self.application_date,
                'reason': self.reason
            },
            'approval': {
                'user': self.approver_name,
                'date': self.approval_date,
                'comment': self.approval_comment
            } if self.approver_id else None,
            'receive': {
                'user': self.receiver_name,
                'date': self.receive_date
            } if self.receiver_id else None
        }
