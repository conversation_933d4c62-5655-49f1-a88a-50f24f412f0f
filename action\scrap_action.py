# -*- coding: utf-8 -*-
"""
物资报废控制器
"""

from flask import Blueprint, request, redirect, url_for, flash
from .base_action import BaseAction
from service.scrap_service import ScrapService
from service.material_service import MaterialService
from service.department_service import DepartmentService

class ScrapAction(BaseAction):
    """物资报废控制器"""
    
    def __init__(self):
        super().__init__()
        self.scrap_service = ScrapService()
        self.material_service = MaterialService()
        self.department_service = DepartmentService()
        self.blueprint = Blueprint('scrap', __name__, url_prefix='/scrap')
        self._register_routes()
    
    def _register_routes(self):
        """注册路由"""
        self.blueprint.add_url_rule('/', 'list', self.list_scraps, methods=['GET'])
        self.blueprint.add_url_rule('/create', 'create', self.create_scrap, methods=['GET', 'POST'])
        self.blueprint.add_url_rule('/<int:scrap_id>', 'detail', self.scrap_detail, methods=['GET'])
        self.blueprint.add_url_rule('/<int:scrap_id>/approve', 'approve', self.approve_scrap, methods=['POST'])
        self.blueprint.add_url_rule('/<int:scrap_id>/reject', 'reject', self.reject_scrap, methods=['POST'])
        self.blueprint.add_url_rule('/<int:scrap_id>/process', 'process', self.process_scrap, methods=['POST'])
        self.blueprint.add_url_rule('/<int:scrap_id>/cancel', 'cancel', self.cancel_scrap, methods=['POST'])
        self.blueprint.add_url_rule('/pending', 'pending', self.pending_scraps, methods=['GET'])
        self.blueprint.add_url_rule('/my', 'my_scraps', self.my_scraps, methods=['GET'])
    
    def list_scraps(self):
        """报废列表"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            query_params = self.get_query_params()
            page = int(query_params.get('page', 1))
            per_page = int(query_params.get('per_page', 20))
            status = query_params.get('status')
            
            # 获取报废列表
            scraps = self.scrap_service.get_all_scraps()
            
            # 过滤
            if status:
                scraps = [s for s in scraps if s.status == status]
            
            # 分页
            paginated_data = self.paginate_data([s.to_dict() for s in scraps], page, per_page)
            
            if request.is_json:
                return paginated_data
            else:
                return self.render_page('scrap/list.html', 
                                      scraps=paginated_data['data'],
                                      pagination=paginated_data['pagination'],
                                      current_status=status)
                                      
        except Exception as e:
            return self.handle_exception(e, "获取报废列表")
    
    def create_scrap(self):
        """创建报废申请"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        if request.method == 'GET':
            materials = self.material_service.get_available_materials()
            departments = self.department_service.get_active_departments()
            return self.render_page('scrap/create.html', materials=materials, departments=departments)
        
        try:
            data = self.get_request_data()
            
            # 调用报废服务创建申请
            result = self.scrap_service.create_scrap_request(data, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "报废申请创建成功",
                url_for('scrap.list') if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "创建报废申请")
    
    def scrap_detail(self, scrap_id):
        """报废详情"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            scrap = self.scrap_service.get_scrap_by_id(scrap_id)
            
            if not scrap:
                if request.is_json:
                    return self.error_response("报废记录不存在", 404)
                else:
                    flash("报废记录不存在", 'error')
                    return redirect(url_for('scrap.list'))
            
            if request.is_json:
                return self.success_response("获取成功", scrap.to_dict())
            else:
                return self.render_page('scrap/detail.html', scrap=scrap)
                
        except Exception as e:
            return self.handle_exception(e, "获取报废详情")
    
    def approve_scrap(self, scrap_id):
        """审批报废申请"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('scrap.list'))
        
        try:
            data = self.get_request_data()
            comment = data.get('comment', '')
            
            # 调用报废服务审批
            result = self.scrap_service.approve_scrap(scrap_id, self.get_current_user_id(), comment)
            
            return self.handle_service_response(
                result,
                "报废申请审批通过",
                url_for('scrap.detail', scrap_id=scrap_id) if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "审批报废申请")
    
    def reject_scrap(self, scrap_id):
        """拒绝报废申请"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('scrap.list'))
        
        try:
            data = self.get_request_data()
            comment = data.get('comment', '')
            
            # 调用报废服务拒绝
            result = self.scrap_service.reject_scrap(scrap_id, self.get_current_user_id(), comment)
            
            return self.handle_service_response(
                result,
                "报废申请已拒绝",
                url_for('scrap.detail', scrap_id=scrap_id) if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "拒绝报废申请")
    
    def process_scrap(self, scrap_id):
        """处理报废物资"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('scrap.list'))
        
        try:
            data = self.get_request_data()
            
            # 验证必需参数
            error = self.validate_required_params(data, ['disposal_method'])
            if error:
                return self.error_response(error)
            
            # 调用报废服务处理
            result = self.scrap_service.process_scrap(
                scrap_id,
                self.get_current_user_id(),
                data['disposal_method']
            )
            
            return self.handle_service_response(
                result,
                "报废物资处理完成",
                url_for('scrap.detail', scrap_id=scrap_id) if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "处理报废物资")
    
    def cancel_scrap(self, scrap_id):
        """取消报废申请"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            # 调用报废服务取消
            result = self.scrap_service.cancel_scrap(scrap_id, self.get_current_user_id())
            
            return self.handle_service_response(
                result,
                "报废申请已取消",
                url_for('scrap.list') if not request.is_json else None
            )
            
        except Exception as e:
            return self.handle_exception(e, "取消报废申请")
    
    def pending_scraps(self):
        """待审批的报废申请"""
        # 检查管理员权限
        if not self.is_admin():
            if request.is_json:
                return self.error_response('权限不足', 403)
            else:
                flash('权限不足', 'error')
                return redirect(url_for('scrap.list'))
        
        try:
            scraps = self.scrap_service.get_pending_scraps()
            
            if request.is_json:
                return self.success_response("获取成功", [s.to_dict() for s in scraps])
            else:
                return self.render_page('scrap/pending.html', scraps=scraps)
                
        except Exception as e:
            return self.handle_exception(e, "获取待审批报废")
    
    def my_scraps(self):
        """我的报废申请"""
        # 检查登录状态
        if not self.is_logged_in():
            if request.is_json:
                return self.error_response('请先登录', 401)
            else:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
        
        try:
            # 这里需要在ScrapService中添加按申请人查询的方法
            # scraps = self.scrap_service.get_scraps_by_applicant(self.get_current_user_id())
            scraps = []  # 临时处理
            
            if request.is_json:
                return self.success_response("获取成功", [s.to_dict() for s in scraps])
            else:
                return self.render_page('scrap/my_scraps.html', scraps=scraps)
                
        except Exception as e:
            return self.handle_exception(e, "获取我的报废申请")
